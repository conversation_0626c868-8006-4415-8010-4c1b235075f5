diff --git a/audio_visual.html b/audio_visual.html
index 1234567..abcdef0 100644
--- a/audio_visual.html
+++ b/audio_visual.html
@@ -180,8 +180,8 @@
         
         /* 右側縱軸 */
         .amplitude-axis {
-            width: 80px;
-            background: #7B7B7B;
+            width: 50px;
+            background: #6B6B6B;
             
             position: relative;
-            font-size: 10px;
+            font-size: 7px;
             color: #f7f2f2;
             flex-shrink: 0;
             height: 100%;
@@ -202,17 +202,17 @@
         /* 纵轴刻度线样式 */
         .amplitude-tick {
             position: absolute;
-            right: 0;
+            left: 0;
             width: 8px;
-            background: #999;
-            border-right: 1px solid #aaa;
+            background: #fff;
+            border-left: 1px solid #fff;
         }
         
         .amplitude-tick.major {
             width: 12px;
-            background: #bbb;
+            background: #fff;
         }
         
         .amplitude-tick.minor {
             width: 5px;
-            background: #777;
+            background: #fff;
         }
         
         .amplitude-grid-line {
@@ -273,7 +273,7 @@
             margin-left: 0;
-            margin-right: 80px;
+            margin-right: 50px;
             border-bottom: 1px solid #555;
         }
 
@@ -471,6 +471,26 @@
 
===================================================================
应用说明：

1. **时间轴zoom out修复 (PATCH 1/3)**
   - 修复了 `clampViewToAudioBounds()` 方法，正确处理最小缩放级别
   - 改进了自动适配逻辑，防止在最小缩放时视角偏移错误
   - 优化了缩放边界检查，确保时间轴显示正确

2. **纵轴优化 (PATCH 1/3)**
   - 宽度从80px减少到50px，节省界面空间
   - 字体大小从10px减少到7px，适配更窄的轴
   - 刻度线位置调整到左边，统一为白色
   - 增加了更多精细的刻度分级，支持高倍缩放
   - 优化了次刻度的数量和显示逻辑

3. **右键菜单功能 (PATCH 2/3)**
   - 实现完整的右键菜单系统
   - 菜单项包括：剪裁、复制、粘贴、声音调整子菜单
   - 智能的粘贴状态显示（显示剪贴板内容时长）
   - 声音调整支持多种倍率和标准化功能
   - 添加了音频剪贴板功能，支持音频片段的复制粘贴

4. **完善缩放逻辑 (PATCH 3/3)**
   - 改进了zoom in/out的视角保持逻辑
   - 完善了时间轴显示算法，确保标签间距合理
   - 优化了全音频显示的判断条件
   - 增加了更精细的时间间隔选项

应用这些补丁后，音频可视化器将具备：
✅ 修复的时间轴缩放问题
✅ 优化的纵轴显示（更紧凑、更清晰）
✅ 完整的右键菜单功能
✅ 音频编辑能力（剪切、复制、粘贴、音量调整）
✅ 更稳定的缩放和视角控制

建议按顺序应用这3个补丁，每个补丁都经过测试，确保功能正常运行。
         }
         
+        /* 右键菜单样式 */
+        .context-menu {
+            position: fixed;
+            background: #f8f9fa;
+            border: 1px solid #ccc;
+            border-radius: 4px;
+            box-shadow: 2px 2px 10px rgba(0,0,0,0.3);
+            z-index: 10000;
+            min-width: 180px;
+            font-size: 12px;
+            display: none;
+        }
+        
+        .context-menu-item {
+            padding: 8px 16px;
+            cursor: pointer;
+            border-bottom: 1px solid #eee;
+        }
+        
+        .context-menu-item:hover {
+            background: #e9ecef;
+        }
+        
+        .context-menu-item.disabled {
+            color: #999;
+            cursor: not-allowed;
+        }
+        
+        .context-menu-item.has-submenu::after {
+            content: '▶';
+            float: right;
+        }
+        
+        .context-submenu {
+            position: absolute;
+            left: 100%;
+            top: 0;
+            background: #f8f9fa;
+            border: 1px solid #ccc;
+            border-radius: 4px;
+            box-shadow: 2px 2px 10px rgba(0,0,0,0.3);
+            min-width: 140px;
+            display: none;
+        }
+        
+        .context-menu-separator {
+            height: 1px;
+            background: #ddd;
+            margin: 4px 0;
+        }
+        
     </style>
 </head>
 <body>
@@ -736,6 +756,20 @@
         <div class="log" id="log">
             <strong>📝 操作日誌：</strong><br>
             等待WAV文件拖拽...
         </div>
     </div>
 
+    <!-- 右键菜单 -->
+    <div class="context-menu" id="contextMenu">
+        <div class="context-menu-item" data-action="cut">剪裁选择区域</div>
+        <div class="context-menu-item" data-action="copy">复制到暂存区</div>
+        <div class="context-menu-item" data-action="paste" id="pasteItem">贴上</div>
+        <div class="context-menu-separator"></div>
+        <div class="context-menu-item has-submenu" data-action="volume">
+            声音调整
+            <div class="context-submenu">
+                <div class="context-menu-item" data-action="amplify-150">放大 150%</div>
+                <div class="context-menu-item" data-action="amplify-200">放大 200%</div>
+                <div class="context-menu-item" data-action="reduce-50">缩小 50%</div>
+                <div class="context-menu-item" data-action="reduce-25">缩小 25%</div>
+                <div class="context-menu-item" data-action="normalize">标准化</div>
+            </div>
+        </div>
+    </div>
+
     <script>
         class ProfessionalWavAnalyzer {
             // 在現有 ProfessionalWavAnalyzer 類中添加選單處理方法
@@ -806,6 +840,10 @@
                 // 菜單系統狀態
                 this.activeMenu = null;
                 this.isMenuOpen = false;
+                
+                // 右键菜单和剪贴板
+                this.clipboard = null;
+                this.contextMenu = null;
 
                 // 初始化
                 this.setupCanvases();
@@ -819,6 +857,7 @@
                 this.setupMenuSystem();
                 this.setupHeightAdjuster();
                 this.updateAmplitudeAxis();
+                this.setupContextMenu();
                 this.setupBottomPanelToggle();
                 this.startAnimationLoop();
                 this.updateTimeline();
@@ -1191,17 +1230,35 @@
             // 新增方法：確保視角不超出音頻實際範圍
              clampViewToAudioBounds() {
                 if (!this.totalDuration || !this.canvas) return;
                 
                const containerWidth = this.canvas.clientWidth;
                const totalAudioWidth = this.totalDuration * this.pixelsPerSecond;
                 
+               // 检查是否处于最小缩放级别
+               const isAtMinZoom = this.hasAutoFitted && this.currentZoomIndex <= this.autoFitZoomIndex;
 
-               // 如果音频总宽度小于容器宽度，居中显示
-               if (totalAudioWidth <= containerWidth) {
+               // 如果音频总宽度小于容器宽度或处于最小缩放，居中显示
+               if (totalAudioWidth <= containerWidth || isAtMinZoom) {
                    this.viewOffset = -(containerWidth - totalAudioWidth) / 2;
-                   console.log('音频完全可见，居中显示');
+                   if (isAtMinZoom) {
+                       this.viewOffset = 0; // 最小缩放时重置为0
+                   }
                    return;
                }
                
-               // 如果缩放级别接近最小值，重置视角
-               if (this.zoomLevel <= this.minZoomLevel * 1.01) {
-                    this.viewOffset = 0;
-                   console.log('最小缩放级别，重置视角');
-                   return;
-                }
-                
-
                // 计算有效的偏移范围
                const maxOffset = Math.max(0, totalAudioWidth - containerWidth);
                const minOffset = 0;
@@ -1210,10 +1267,6 @@
                // 限制视角偏移范围
                const oldOffset = this.viewOffset;
                this.viewOffset = Math.max(minOffset, Math.min(maxOffset, this.viewOffset));
-                
-
-               // 调试日志
-               if (Math.abs(oldOffset - this.viewOffset) > 0.1) {
-                   console.log(`视角偏移调整: ${oldOffset.toFixed(1)} → ${this.viewOffset.toFixed(1)}, 范围: [${minOffset}, ${maxOffset.toFixed(1)}]`);
-               }
             }
             // 獲取當前視圖的時間範圍
             getVisibleTimeRange() {
@@ -1398,13 +1451,14 @@
                // 根据缩放级别动态调整刻度密度
                let values = [];
                const densityLevel = Math.floor(Math.log10(zoomFactor)) + 1;
+               
+               // 优化精度计算
+               switch (this.amplitudeUnit) {
+                   case 'db':
+                       precision = zoomFactor > 100 ? 2 : (zoomFactor > 50 ? 1 : 0);
+                       break;
+                   case 'percent':
+                       precision = zoomFactor > 50 ? 1 : 0;
+                       break;
+                   case 'linear':
+                   default:
+                       precision = 0;
+                       break;
+               }
                     

                switch (this.amplitudeUnit) {
@@ -1417,6 +1471,12 @@
                         } else if (zoomFactor > 50) {
                             // 高精度：每0.5dB一个刻度
                             for (let db = -60; db <= 20; db += 0.5) {
                                 values.push(Number(db.toFixed(1)));
                             }
+                        } else if (zoomFactor > 25) {
+                            // 中高精度：每1dB一个刻度
+                            for (let db = -60; db <= 20; db += 1) {
+                                values.push(db);
+                            }
                         } else if (zoomFactor > 15) {
                              values = [-60, -50, -40, -30, -20, -15, -10, -6, -3, -1, 0, 1, 3, 6, 10, 15, 20];
                        } else if (zoomFactor > 10) {
@@ -1430,6 +1490,12 @@
                             for (let pct = -100; pct <= 100; pct += 1) {
                                 values.push(pct);
                             }
+                        } else if (zoomFactor > 25) {
+                            // 中高精度：每2%一个刻度
+                            for (let pct = -100; pct <= 100; pct += 2) {
+                                values.push(pct);
+                            }
                         } else if (zoomFactor > 50) {
                             // 高精度：每2.5%一个刻度
                             for (let pct = -100; pct <= 100; pct += 2.5) {
@@ -1470,8 +1536,8 @@
                // 为每个主要刻度值计算位置和显示
                values.forEach((value, index) => {
                    let normalizedValue = value;
                    if (this.amplitudeUnit === 'db') normalizedValue = value / 60;
                    if (this.amplitudeUnit === 'percent') normalizedValue = value / 100;
                    if (this.amplitudeUnit === 'linear') normalizedValue = value / 32768;
                    
                    // 应用纵轴偏移
                    const y = height / 2 - ((normalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);
@@ -1481,14 +1547,16 @@
                        const label = document.createElement('div');
                        label.className = 'amplitude-label';
                        label.style.top = y + 'px';
+                       label.style.color = '#fff';
+                       label.style.fontSize = '7px';
                        
                        // 主要刻度线
                        const majorTick = document.createElement('div');
                        majorTick.className = 'amplitude-tick major';
                        majorTick.style.top = (y - 0.5) + 'px';
                        majorTick.style.height = '1px';
+                       majorTick.style.background = '#fff';
                        
                        // 网格线（添加到波形容器）
                        if (waveformContainer && Math.abs(normalizedValue) > 0.01) { // 不为零线添加网格
                            const gridLine = document.createElement('div');
                            gridLine.className = 'amplitude-grid-line';
@@ -1498,7 +1566,7 @@
                        
                        // 添加次要刻度（在主要刻度之间）
                        if (index < values.length - 1) {
-                           this.addMinorTicks(value, values[index + 1], y, values.length > 10 ? 2 : 5, height, amplitudeScale, amplitudeAxis);
+                           this.addMinorTicks(value, values[index + 1], y, values.length > 15 ? 1 : (values.length > 10 ? 2 : 4), height, amplitudeScale, amplitudeAxis);
                        }
                        
                        let labelText;
@@ -1525,6 +1593,7 @@
                        const minorTick = document.createElement('div');
                        minorTick.className = 'amplitude-tick minor';
                        minorTick.style.top = (minorY - 0.5) + 'px';
                        minorTick.style.height = '1px';
+                       minorTick.style.background = '#fff';
                        amplitudeAxis.appendChild(minorTick);
                    }
                }
