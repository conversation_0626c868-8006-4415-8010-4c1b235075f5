# -*- coding: utf-8 -*-
"""
Created on Mon Jul  7 13:42:03 2025

@author: user
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.io import wavfile
import librosa

def calculate_spectrogram_basic(audio_data, sample_rate, window='hann', nperseg=1024, noverlap=512):
    """
    計算音頻的 spectrogram
    
    Parameters:
    - audio_data: 音頻數據 (numpy array)
    - sample_rate: 採樣率
    - window: 窗函數類型
    - nperseg: 每個段的長度
    - noverlap: 重疊長度
    """
    frequencies, times, Sxx = signal.spectrogram(
        audio_data, 
        fs=sample_rate,
        window=window,
        nperseg=nperseg,
        noverlap=noverlap
    )
    
    return frequencies, times, Sxx

# 使用範例
if __name__ == "__main__":
    # 讀取音頻文件
    sample_rate, audio_data = wavfile.read('custom_test_ringtone.wav')
    
    # 如果是立體聲，取單聲道
    if len(audio_data.shape) > 1:
        audio_data = audio_data[:, 0]
    
    # 計算 spectrogram
    frequencies, times, Sxx = calculate_spectrogram_basic(audio_data, sample_rate)
    
    # 繪製 spectrogram
    plt.figure(figsize=(12, 8))
    plt.pcolormesh(times, frequencies, 10 * np.log10(Sxx), shading='gouraud')
    plt.ylabel('頻率 [Hz]')
    plt.xlabel('時間 [s]')
    plt.colorbar(label='功率 [dB]')
    plt.title('Spectrogram')
    plt.show()