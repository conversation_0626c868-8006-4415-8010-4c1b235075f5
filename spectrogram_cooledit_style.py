# -*- coding: utf-8 -*-
"""
Created on Mon Jul  7 14:30:32 2025

@author: user
"""

def create_cooledit_style_spectrogram(audio_file, output_file=None):
    """
    重現 Cool Edit Pro 經典的 spectrogram 風格
    """
    import librosa
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 載入音頻
    y, sr = librosa.load(audio_file, sr=44100)  # Cool Edit 常用 44.1kHz
    
    # Cool Edit Pro 典型參數
    n_fft = 2048        # 標準 FFT 大小
    hop_length = 512    # 75% 重疊
    window = 'hamming'  # Cool Edit 預設窗函數
    
    # 計算 STFT
    stft = librosa.stft(
        y, 
        n_fft=n_fft, 
        hop_length=hop_length, 
        window=window
    )
    
    # 轉換為 magnitude spectrogram
    magnitude = np.abs(stft)
    magnitude_db = librosa.amplitude_to_db(magnitude, ref=np.max)
    
    # 設置顯示參數（模擬 Cool Edit Pro 的外觀）
    plt.figure(figsize=(16, 10))
    
    # 使用接近 Cool Edit Pro 的顏色方案
    librosa.display.specshow(
        magnitude_db,
        sr=sr,
        hop_length=hop_length,
        x_axis='time',
        y_axis='log',           # 對數頻率軸
        cmap='hot',             # 接近 Cool Edit 的色彩
        vmin=-60,               # 動態範圍下限
        vmax=0,                 # 動態範圍上限
        fmax=sr/2               # 顯示到 Nyquist 頻率
    )
    
    # 設置外觀
    plt.colorbar(format='%+2.0f dB', label='幅度 (dB)')
    plt.title('Cool Edit Pro Style Spectrogram', fontsize=14, fontweight='bold')
    plt.xlabel('時間 (s)', fontsize=12)
    plt.ylabel('頻率 (Hz)', fontsize=12)
    
    # 使用暗色背景（類似 Cool Edit）
    plt.style.use('dark_background')
    
    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
    
    plt.show()
    
    return magnitude_db, sr

# 使用範例
spec_data, sample_rate = create_cooledit_style_spectrogram('custom_test_ringtone.wav')