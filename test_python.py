#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Python测试脚本
"""

print("🐍 Python测试开始...")

try:
    import sys
    print(f"✅ Python版本: {sys.version}")
    
    import numpy as np
    print(f"✅ NumPy版本: {np.__version__}")
    
    import flask
    print(f"✅ Flask版本: {flask.__version__}")
    
    import librosa
    print(f"✅ Librosa版本: {librosa.__version__}")
    
    print("🎉 所有依赖都已正确安装！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)

print("🚀 准备启动服务器...")
