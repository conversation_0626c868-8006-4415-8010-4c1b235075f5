diff --git a/audio_visual.html b/audio_visual.html
index abcdef0..fedcba9 100644
--- a/audio_visual.html
+++ b/audio_visual.html
@@ -1600,6 +1600,204 @@
                }
            }
                
+           // 设置右键菜单
+           setupContextMenu() {
+               this.contextMenu = document.getElementById('contextMenu');
+               const waveformContainer = document.querySelector('.waveform-container');
+               
+               // 右键事件
+               waveformContainer.addEventListener('contextmenu', (e) => {
+                   e.preventDefault();
+                   this.showContextMenu(e.clientX, e.clientY);
+               });
+               
+               // 菜单项点击事件
+               document.querySelectorAll('.context-menu-item').forEach(item => {
+                   item.addEventListener('click', (e) => {
+                       e.stopPropagation();
+                       if (!item.classList.contains('disabled') && !item.classList.contains('has-submenu')) {
+                           const action = item.dataset.action;
+                           this.executeContextAction(action);
+                           this.hideContextMenu();
+                       }
+                   });
+               });
+               
+               // 子菜单显示/隐藏
+               const volumeItem = document.querySelector('[data-action="volume"]');
+               const submenu = volumeItem.querySelector('.context-submenu');
+               
+               volumeItem.addEventListener('mouseenter', () => {
+                   submenu.style.display = 'block';
+               });
+               
+               volumeItem.addEventListener('mouseleave', () => {
+                   setTimeout(() => {
+                       if (!submenu.matches(':hover')) {
+                           submenu.style.display = 'none';
+                       }
+                   }, 100);
+               });
+               
+               submenu.addEventListener('mouseleave', () => {
+                   submenu.style.display = 'none';
+               });
+               
+               // 点击其他地方隐藏菜单
+               document.addEventListener('click', () => {
+                   this.hideContextMenu();
+               });
+               
+               // 更新贴上状态
+               this.updatePasteStatus();
+           }
+           
+           showContextMenu(x, y) {
+               if (!this.audioData) return;
+               
+               this.updatePasteStatus();
+               
+               this.contextMenu.style.left = x + 'px';
+               this.contextMenu.style.top = y + 'px';
+               this.contextMenu.style.display = 'block';
+               
+               // 确保菜单不超出屏幕
+               const rect = this.contextMenu.getBoundingClientRect();
+               if (rect.right > window.innerWidth) {
+                   this.contextMenu.style.left = (x - rect.width) + 'px';
+               }
+               if (rect.bottom > window.innerHeight) {
+                   this.contextMenu.style.top = (y - rect.height) + 'px';
+               }
+           }
+           
+           hideContextMenu() {
+               this.contextMenu.style.display = 'none';
+               document.querySelectorAll('.context-submenu').forEach(submenu => {
+                   submenu.style.display = 'none';
+               });
+           }
+           
+           updatePasteStatus() {
+               const pasteItem = document.getElementById('pasteItem');
+               if (this.clipboard && this.clipboard.length > 0) {
+                   pasteItem.classList.remove('disabled');
+                   pasteItem.textContent = `贴上 (${this.formatTime(this.clipboard.length / this.sampleRate)})`;
+               } else {
+                   pasteItem.classList.add('disabled');
+                   pasteItem.textContent = '贴上';
+               }
+           }
+           
+           executeContextAction(action) {
+               switch(action) {
+                   case 'cut':
+                       this.cutSelection();
+                       break;
+                   case 'copy':
+                       this.copySelection();
+                       break;
+                   case 'paste':
+                       this.pasteFromClipboard();
+                       break;
+                   case 'amplify-150':
+                       this.amplifySelection(1.5);
+                       break;
+                   case 'amplify-200':
+                       this.amplifySelection(2.0);
+                       break;
+                   case 'reduce-50':
+                       this.amplifySelection(0.5);
+                       break;
+                   case 'reduce-25':
+                       this.amplifySelection(0.25);
+                       break;
+                   case 'normalize':
+                       this.normalizeSelection();
+                       break;
+               }
+           }
+           
+           cutSelection() {
+               if (this.selectionStart >= this.selectionEnd) {
+                   this.log('❌ 请先选择一个区域');
+                   return;
+               }
+               
+               this.copySelection();
+               
+               const startSample = Math.floor(this.selectionStart * this.sampleRate);
+               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
+               
+               // 创建新的音频数组，移除选中部分
+               const newAudioData = new Float32Array(this.audioData.length - (endSample - startSample));
+               let writeIndex = 0;
+               
+               // 复制选择前的部分
+               for (let i = 0; i < startSample; i++) {
+                   newAudioData[writeIndex++] = this.audioData[i];
+               }
+               
+               // 复制选择后的部分
+               for (let i = endSample; i < this.audioData.length; i++) {
+                   newAudioData[writeIndex++] = this.audioData[i];
+               }
+               
+               this.audioData = newAudioData;
+               this.totalDuration = this.audioData.length / this.sampleRate;
+               
+               // 清除选择
+               this.selectionStart = this.selectionEnd = 0;
+               document.getElementById('selectionBox').style.display = 'none';
+               
+               this.updateAudioBuffer();
+               this.autoFitWaveform();
+               
+               this.log(`✂️ 剪裁完成，新时长: ${this.formatTime(this.totalDuration)}`);
+           }
+           
+           copySelection() {
+               if (this.selectionStart >= this.selectionEnd) {
+                   this.log('❌ 请先选择一个区域');
+                   return;
+               }
+               
+               const startSample = Math.floor(this.selectionStart * this.sampleRate);
+               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
+               
+               this.clipboard = new Float32Array(endSample - startSample);
+               for (let i = 0; i < this.clipboard.length; i++) {
+                   this.clipboard[i] = this.audioData[startSample + i];
+               }
+               
+               const duration = this.clipboard.length / this.sampleRate;
+               this.log(`📋 已复制 ${this.formatTime(duration)} 到暂存区`);
+           }
+           
+           pasteFromClipboard() {
+               if (!this.clipboard || this.clipboard.length === 0) {
+                   this.log('❌ 暂存区为空');
+                   return;
+               }
+               
+               // 简单的粘贴到选择位置
+               const insertPosition = this.selectionStart > 0 ? 
+                   Math.floor(this.selectionStart * this.sampleRate) : 0;
+               
+               const newAudioData = new Float32Array(this.audioData.length + this.clipboard.length);
+               let writeIndex = 0;
+               
+               // 复制插入位置前的数据
+               for (let i = 0; i < insertPosition; i++) {
+                   newAudioData[writeIndex++] = this.audioData[i];
+               }
+               
+               // 插入剪贴板数据
+               for (let i = 0; i < this.clipboard.length; i++) {
+                   newAudioData[writeIndex++] = this.clipboard[i];
+               }
+               
+               // 复制插入位置后的数据
+               for (let i = insertPosition; i < this.audioData.length; i++) {
+                   newAudioData[writeIndex++] = this.audioData[i];
+               }
+               
+               this.audioData = newAudioData;
+               this.totalDuration = this.audioData.length / this.sampleRate;
+               
+               this.updateAudioBuffer();
+               this.autoFitWaveform();
+               
+               const duration = this.clipboard.length / this.sampleRate;
+               this.log(`📌 已粘贴 ${this.formatTime(duration)}，新时长: ${this.formatTime(this.totalDuration)}`);
+           }
+           
+           amplifySelection(factor) {
+               if (this.selectionStart >= this.selectionEnd) {
+                   this.log('❌ 请先选择一个区域');
+                   return;
+               }
+               
+               const startSample = Math.floor(this.selectionStart * this.sampleRate);
+               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
+               
+               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
+                   this.audioData[i] = Math.max(-1, Math.min(1, this.audioData[i] * factor));
+               }
+               
+               this.updateAudioBuffer();
+               
+               const percentage = Math.round(factor * 100);
+               this.log(`🔊 选择区域音量调整为 ${percentage}%`);
+           }
+           
+           normalizeSelection() {
+               if (this.selectionStart >= this.selectionEnd) {
+                   this.log('❌ 请先选择一个区域');
+                   return;
+               }
+               
+               const startSample = Math.floor(this.selectionStart * this.sampleRate);
+               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
+               
+               // 找到最大振幅
+               let maxAmplitude = 0;
+               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
+                   maxAmplitude = Math.max(maxAmplitude, Math.abs(this.audioData[i]));
+               }
+               
+               if (maxAmplitude === 0) {
+                   this.log('⚠️ 选择区域无信号');
+                   return;
+               }
+               
+               // 计算标准化因子（留出一点余量）
+               const normalizeFactor = 0.95 / maxAmplitude;
+               
+               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
+                   this.audioData[i] *= normalizeFactor;
+               }
+               
+               this.updateAudioBuffer();
+               
+               this.log(`📏 选择区域已标准化，增益: ${(20 * Math.log10(normalizeFactor)).toFixed(1)}dB`);
+           }
+           
+           updateAudioBuffer() {
+               if (this.audioContext && this.audioData) {
+                   try {
+                       this.audioBuffer = this.audioContext.createBuffer(1, this.audioData.length, this.sampleRate);
+                       const channelData = this.audioBuffer.getChannelData(0);
+                       for (let i = 0; i < this.audioData.length; i++) {
+                           channelData[i] = this.audioData[i];
+                       }
+                   } catch (error) {
+                       this.log('⚠️ 音频缓冲区更新失败: ' + error.message);
+                   }
+               }
+           }
+               
            updateLayout() {
                // 同步更新時間軸和縱軸
               this.updateTimeline();

