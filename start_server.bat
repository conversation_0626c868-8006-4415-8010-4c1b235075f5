@echo off
echo 🚀 启动Spectrogram Python后端服务器...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查是否存在虚拟环境
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📚 安装Python依赖...
pip install -r requirements.txt

REM 启动服务器
echo 🌐 启动Flask服务器...
echo 服务器将在 http://localhost:5000 运行
echo 按 Ctrl+C 停止服务器
echo.
python spectrogram_server.py

pause
