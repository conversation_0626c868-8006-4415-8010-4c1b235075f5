<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 在现有样式前添加菜单样式 -->
    <style>
        /* 菜单栏样式 */
        .menu-bar {
            background: linear-gradient(to bottom, #f0f0f0, #d0d0d0);
            border-bottom: 1px solid #999;
            padding: 0;
            display: flex;
            font-size: 13px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: #000;
            user-select: none;
            z-index: 1000;
            position: relative;
        }

        .menu-item {
            position: relative;
            padding: 6px 12px;
            cursor: pointer;
            border-right: 1px solid transparent;
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            background: linear-gradient(to bottom, #e0e8ff, #c0d0ff);
            border-color: #8bb3ff;
        }

        .menu-item.active {
            background: linear-gradient(to bottom, #c0d0ff, #a0c0ff);
            border-color: #6699ff;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 180px;
            background: #f8f8f8;
            border: 1px solid #999;
            border-top: none;
            box-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            display: none;
            z-index: 1001;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            padding: 6px 20px 6px 12px;
            cursor: pointer;
            border-bottom: 1px solid #e0e0e0;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .dropdown-item:hover {
            background: #e0e8ff;
        }

        .dropdown-item.disabled {
            color: #999;
            cursor: not-allowed;
        }

        .dropdown-shortcut {
            font-size: 11px;
            color: #666;
            margin-left: 20px;
        }

        .dropdown-separator {
            height: 1px;
            background: #ccc;
            margin: 2px 0;
        }
    </style>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>專業級音頻可視化器 - 增強版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #2a2a2a;
            color: #e0e0e0;
            min-height: 100vh;
        }
        
        /* 專業工具欄 */
        .toolbar {
            background: linear-gradient(to bottom, #4a4a4a, #3a3a3a);
            border-bottom: 1px solid #555;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
            font-size: 12px;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 10px;
            border-right: 1px solid #555;
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .btn {
            background: linear-gradient(to bottom, #5a5a5a, #4a4a4a);
            border: 1px solid #666;
            color: #e0e0e0;
            padding: 4px 12px;
            font-size: 11px;
            cursor: pointer;
            border-radius: 2px;
            font-family: inherit;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: linear-gradient(to bottom, #6a6a6a, #5a5a5a);
        }

        .btn:active, .btn.active {
            background: linear-gradient(to bottom, #3a3a3a, #4a4a4a);
            border-color: #00ff88;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .input-small {
            background: #333;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 2px 6px;
            width: 80px;
            font-size: 11px;
            font-family: inherit;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4444;
            margin-right: 5px;
        }

        .status-indicator.connected {
            background: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .time-display {
            background: #1a1a1a;
            border: 1px solid #555;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            color: #00ff88;
            text-align: center;
            min-width: 120px;
            border-radius: 3px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .zoom-slider {
            width: 100px;
            accent-color: #00ff88;
        }

        .amplitude-select {
            background: #333;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 2px 6px;
            font-size: 11px;
            font-family: inherit;
        }
        .checkbox-control {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .resize-handle {
           height: 5px;
            background: #555;
            cursor: ns-resize;
            border-top: 1px solid #666;
            border-bottom: 1px solid #666;
            user-select: none;
            position: relative;
            z-index: 10;
            flex-shrink: 0;
        }
        
        .resize-handle:hover {
            background: #777;
        }
        
        .resize-handle::before {
            content: '⋯';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            color: #aaa;
            font-size: 12px;
            letter-spacing: 2px;
        }
        
        .bottom-panel.hidden {
         display: none;
         }
        .test-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 120px); /* 为菜单栏留出空间 */
            background: #1a1a1a;
            border: 1px solid #333;
            overflow: hidden;
        }
        
        .main-area {
            flex: 0 0 45vh;
            min-height: 200px;
            max-height: 70vh;
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
         /* 添加高度调整条样式 */
        .height-adjuster {
            height: 8px;
            background: linear-gradient(to bottom, #4a4a4a, #3a3a3a);
            border-top: 1px solid #555;
            border-bottom: 1px solid #555;
            cursor: ns-resize;
            position: relative;
            user-select: none;
            flex-shrink: 0;
        }

        
        .height-adjuster:hover {
            background: linear-gradient(to bottom, #5a5a5a, #4a4a4a);
        }
        
        .height-adjuster::before {
            content: '═══';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            color: #888;
            font-size: 10px;
            letter-spacing: 1px;
        }
        
        /* 修改拖拽樣式 */
        .drop-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 200px;
            border: 3px dashed #007acc;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            opacity: 1;
            visibility: visible;
        }
        
        .drop-zone.dragover {
            border-color: #28a745;
            background: #e8f5e8;
            transform: translate(-50%, -50%) scale(1.05);
        }
        
        .drop-zone-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .drop-zone-text {
            font-size: 18px;
            color: #333;
            text-align: center;
        }
        
        .drop-zone-subtext {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .drop-zone.hidden {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        /* 波形顯示區域 */
        .waveform-container {
            flex: 1;
            position: relative;
            background: #f7f3f3;
            overflow: hidden;
            min-height: 0;
            height: 100%;
            z-index: 0;
        }
                
        /* 右側縱軸 */
        .amplitude-axis {
            width: 50px;
            background: #7B7B7B;
            position: relative;
            font-size: 8px;
            color: #f7f2f2;
            flex-shrink: 0;
            height: 100%;
            overflow: visible;
            z-index: 10;
        }

        .amplitude-label {
            position: absolute;
            color: #fff;
            font-size: 8px;
            font-family: monospace;
            white-space: nowrap;
            pointer-events: none;
            user-select: none;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
        
        .amplitude-label {
            position: absolute;
            right: 5px;
            transform: translateY(-50%);
            white-space: nowrap;
            font-family: 'Courier New', monospace;
        }
        
        /* 纵轴刻度线样式 */
        .amplitude-tick {
            position: absolute;
            left: 0;
            width: 8px;
            background: #999;
            border-left: 1px solid #fff;
        }

        .amplitude-tick.major {
            width: 12px;
            background: #fff;
            border-right: 2px solid #fff;
        }

        .amplitude-tick.minor {
            width: 5px;
            background: rgba(255, 255, 255, 0.6);
            border-right: 1px solid rgba(255, 255, 255, 0.4);
            transition: opacity 0.2s ease;
        }

        /* 副刻度标签样式 */
        .amplitude-label.minor {
            font-family: 'Courier New', monospace;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.7);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            pointer-events: none;
            user-select: none;
        }
        
        .amplitude-grid-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.15);
            pointer-events: none;
            z-index: 1;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .waveform-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
            display: block;
            object-fit: contain;
            position: relative;
            z-index: 0;
        }

        .waveform-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff88;
            padding: 5px 10px;
            font-size: 11px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            z-index: 5;
        }

        .file-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffff88;
            padding: 8px 12px;
            font-size: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            z-index: 5;
            display: none;
            max-width: 300px;
        }

        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 5;
        }

        .crosshair-h {
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
        }

        .crosshair-v {
            width: 1px;
            height: 100%;
            background: rgba(255, 255, 255, 0.5);
        }

        .sample-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            border: 1px solid #555;
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        /* 顯示模式控制按鈕 */
        .display-mode-controls {
            display: flex;
            gap: 2px;
        }

        .display-mode-btn {
            padding: 4px 6px;
            border: 1px solid #555;
            background: #3a3a3a;
            color: #ccc;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .display-mode-btn:hover {
            background: #4a4a4a;
            border-color: #666;
        }

        .display-mode-btn.active {
            background: #0078d4;
            border-color: #106ebe;
            color: #fff;
        }

        .display-mode-btn svg {
            width: 16px;
            height: 16px;
        }

        /* 頻譜圖樣式 */
        .spectrogram-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .spectrogram-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            border: 1px solid #555;
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .selection-box {
            position: absolute;
            border: 2px solid #ffff00;
            background: rgba(255, 255, 0, 0.2);
            pointer-events: none;
            z-index: 3;
            box-shadow: 0 0 4px rgba(255, 255, 0, 0.5);
        }

        .playhead {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ffff00;
            z-index: 4;
            pointer-events: none;
        }

        .playhead::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 8px solid #ffff00;
        }

        .resize-handle {
            height: 5px;
            background: #555;
            cursor: ns-resize;
            border-top: 1px solid #666;
            border-bottom: 1px solid #666;
            user-select: none;
        }

                
        .waveform-with-axis {
            display: flex;
            flex: 1;
            min-height: 0;
            height: 100%;
            align-items: stretch;
        }
        /* 時間軸 */
        .timeline {
            background: #7B7B7B;
            height: 25px;
           min-height: 25px;
           flex-shrink: 0;
            position: relative;
            font-size: 10px;
            color: #f5f1f1;
            margin-left: 0;
            margin-right: 50px;
            border-bottom: 1px solid #555;
        }

        .timeline-marker {
            position: absolute;
            top: 0;
            bottom: 0;
            border-left: 1px solid #555;
            padding-left: 3px;
            padding-top: 2px;
            font-family: 'Courier New', monospace;
        }

        .timeline-marker.major {
            border-left-color: #888;
            font-weight: bold;
        }
        
        .timeline-tick {
            position: absolute;
            bottom: 0;
            width: 2px;
            background: #0a0a0a;
        }

        .timeline-tick.major {
            height: 15px;
            background: #0f0e0e;
        }

        .timeline-tick.minor {
            height: 8px;
       }
        /* 底部面板 */
        .bottom-panel {
            background: #2a2a2a;
            flex: 1 1 auto;
            border-top: 1px solid #555;
            min-height: 150px;
            display: flex;
            overflow: hidden;
        }
        .log {
            background: #1a1a1a;
            border-top: 1px solid #555;
            padding: 8px 10px;
            max-height: 80px;
            min-height: 60px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 10px;
            color: #888;
            flex-shrink: 0;
        }
   
        .spectrum-panel {
            flex: 2;
            border-right: 1px solid #555;
            position: relative;
            min-width: 0;
        }

        .spectrum-canvas {
            width: 90%;
            height: 90%;
            background: #000;
        }

        .frequency-labels {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 40px;
            background: #2a2a2a;
            border-right: 1px solid #555;
            font-size: 9px;
            color: #aaa;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 5px 2px;
        }

        .meters-panel {
            flex: 0 0 200px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            min-width: 0;
        }

        .meter-group {
            background: #333;
            border: 1px solid #555;
            border-radius: 3px;
            padding: 8px;
        }

        .meter-title {
            font-size: 10px;
            color: #aaa;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .level-meter {
            height: 20px;
            background: #1a1a1a;
            border: 1px solid #555;
            position: relative;
            overflow: hidden;
        }

        .level-bar {
            height: 100%;
            background: linear-gradient(to right, #00ff00 0%, #ffff00 70%, #ff0000 100%);
            width: 0%;
            transition: width 0.1s ease;
        }

        .level-text {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #fff;
            text-shadow: 1px 1px 1px #000;
        }
        
        .selection-info {
            background: #333;
            border: 1px solid #555;
            padding: 5px 8px;
            font-size: 10px;
            border-radius: 2px;
        }

        .selection-info span {
            margin-right: 15px;
        }
        


        @media (max-width: 1024px) {
            .test-container {
                height: calc(100vh - 130px);
            }
            .main-area {
                flex: 0 0 35vh;
                max-height: 50vh;
            }
            .bottom-panel {
                flex-direction: column;
                flex: 1;
                min-height: 120px;
            }
            
            .spectrum-panel {
                border-right: none;
                border-bottom: 1px solid #555;
                flex: 1;
            }
            
            .meters-panel {
                flex: 0 0 80px;
            }

            .drop-zone {
                width: 300px;
                height: 150px;
                font-size: 16px;
            }
            .log {
                max-height: 60px;
                min-height: 40px;
            }
            
        /* 右键菜单样式 */
        .context-menu {
            position: fixed;
            background: #ffffff !important;
            border: 1px solid #cccccc !important;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);

            z-index: 10000;
            min-width: 180px;
            font-size: 12px;
            display: none;
         }
        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 1px solid #eeeeee;
            color: #333333;
            background: #ffffff;
        }
        .context-menu-item:last-child {
            border-bottom: none;
         }
        .context-menu-item:hover {
            background: #007acc !important;
            color: #ffffff !important;
        }
        
        .context-menu-item.disabled {
            color: #999999;
            cursor: not-allowed;
            background: #ffffff;
        }
        .context-menu-item.disabled:hover {
            background: #ffffff;
            color: #999999;
         }
        .context-menu-item.has-submenu::after {
            content: '▶';
            float: right;
            color: #666666;
        }
        .context-menu-item.has-submenu:hover::after {
            color: #ffffff;
         }
        .context-submenu {
            position: absolute;
            left: 100%;
            top: 0;
            background: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            min-width: 140px;
            display: none;
            
         }
        .context-menu-separator {
            height: 1px;
            background: #dddddd;
            margin: 4px 0;
            border: none;
        }
        /* 菜单背景遮罩 - 移除模糊效果 */
        .context-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            
            z-index: 9999;
            display: none;
           
         }
    </style>
</head>
<body>
    <!-- 添加菜單欄 -->
    <div class="menu-bar">
        <div class="menu-item" data-menu="file">
            檔案
            <div class="dropdown-menu">
                <div class="dropdown-item" data-action="open">打開WAV檔案...<span class="dropdown-shortcut">Ctrl+O</span></div>
                <div class="dropdown-separator"></div>
                <div class="dropdown-item" data-action="save-project">儲存專案<span class="dropdown-shortcut">Ctrl+S</span></div>
                <div class="dropdown-item" data-action="export-audio">匯出音訊...</div>
                <div class="dropdown-separator"></div>
                <div class="dropdown-item" data-action="exit">離開</div>
            </div>
        </div>

        <div class="menu-item" data-menu="edit">
            編輯
            <div class="dropdown-menu">
                <div class="dropdown-item" data-action="select-all">全選<span class="dropdown-shortcut">Ctrl+A</span></div>
                <div class="dropdown-item" data-action="clear-selection">清除選取</div>
                <div class="dropdown-separator"></div>
                <div class="dropdown-item" data-action="copy-selection">複製選取</div>
            </div>
        </div>

        <div class="menu-item" data-menu="view">
            檢視
            <div class="dropdown-menu">
                <div class="dropdown-item" data-action="zoom-in">放大<span class="dropdown-shortcut">+</span></div>
                <div class="dropdown-item" data-action="zoom-out">縮小<span class="dropdown-shortcut">-</span></div>
                <div class="dropdown-item" data-action="zoom-fit">適應視窗</div>
                <div class="dropdown-separator"></div>
                <div class="dropdown-item" data-action="toggle-spectrum">切換頻譜顯示</div>
                <div class="dropdown-item" data-action="toggle-meters">切換音量表</div>
            </div>
        </div>

        <div class="menu-item" data-menu="analyze">
            分析
            <div class="dropdown-menu">
                <div class="dropdown-item" data-action="show-info">顯示檔案資訊</div>
                <div class="dropdown-item" data-action="plot-spectrum">繪製頻譜</div>
                <div class="dropdown-item" data-action="amplitude-stats">振幅統計</div>
            </div>
        </div>

        <div class="menu-item" data-menu="help">
            說明
            <div class="dropdown-menu">
                <div class="dropdown-item" data-action="shortcuts">快捷鍵說明</div>
                <div class="dropdown-item" data-action="about">關於分析器</div>
            </div>
        </div>
    </div>
    <!-- 專業工具欄 -->
    <div class="toolbar">
        <div class="toolbar-group">
            <div class="status-indicator" id="connectionStatus"></div>
            <span id="statusText">WAV文件分析器</span>
        </div>
        
        <div class="toolbar-group">
            <button class="btn" id="connectBtn">連接RTP</button>
            <button class="btn" id="recordBtn">錄製</button>
            <button class="btn" id="playBtn">播放</button>
            <button class="btn" id="stopBtn">停止</button>
        </div>
        
        <div class="toolbar-group">
            <div class="time-display" id="timeDisplay">0:00.000</div>
        </div>
        
        <div class="toolbar-group">
            <div class="selection-info">
                <span>Begin: <span id="selBegin">0:00.000</span></span>
                <span>End: <span id="selEnd">0:00.000</span></span>
                <span>Length: <span id="selLength">0:00.000</span></span>
            </div>
        </div>
        
        <div class="toolbar-group">
            <label>時間軸:</label>
            <div class="zoom-controls">
                <button class="btn" id="zoomOut">-</button>
                <input type="range" class="zoom-slider" id="zoomSlider" min="1" max="10000" value="10">
                <button class="btn" id="zoomIn">+</button>
            </div>
        </div>
        
        <div class="toolbar-group">
            <label>振幅:</label>
            <div class="zoom-controls">
                <button class="btn" id="ampZoomOut">-</button>
                <input type="range" class="zoom-slider" id="ampSlider" min="1" max="20" value="10">
                <button class="btn" id="ampZoomIn">+</button>
                <select class="amplitude-select" id="ampUnit">
                    <option value="linear">線性</option>
                    <option value="db">dB</option>
                    <option value="percent">%</option>
                </select>
            </div>
        </div>
       
        <div class="toolbar-group">
            <label>縮放步進:</label>
            <div class="zoom-controls">
                <input type="number" class="input-small" id="zoomStep" min="1" max="100" value="10" style="width: 60px;">
                <span style="font-size: 10px;">倍</span>
            </div>
        </div>
        <div class="toolbar-group">
            <div class="checkbox-control">
            <input type="checkbox" id="showBottomPanel" checked>
            <label for="showBottomPanel">顯示底部面板</label>
            </div>
        </div>

        <div class="toolbar-group">
            <label>顯示模式:</label>
            <div class="display-mode-controls">
                <button class="btn display-mode-btn active" id="waveformModeBtn" title="波形顯示">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 2v12M6 4v8M10 4v8M4 6v4M12 6v4M2 7v2M14 7v2"/>
                    </svg>
                </button>
                <button class="btn display-mode-btn" id="spectrogramModeBtn" title="頻譜圖顯示">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <rect x="1" y="1" width="2" height="14" opacity="0.3"/>
                        <rect x="4" y="3" width="2" height="12" opacity="0.5"/>
                        <rect x="7" y="2" width="2" height="13" opacity="0.7"/>
                        <rect x="10" y="4" width="2" height="11" opacity="0.9"/>
                        <rect x="13" y="1" width="2" height="14" opacity="0.6"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="test-container">
        <div class="main-area">
            <!-- 拖拽區域 -->
            <div class="drop-zone" id="dropZone">
                <div class="drop-zone-icon">📁</div>
                <div class="drop-zone-text">將WAV文件拖拽到此處</div>
                <div class="drop-zone-subtext">支持WAV格式，或點擊此處選擇文件</div>
            </div>
            <div class="waveform-info" id="waveformInfo">
                時間軸: 1:1 | 振幅: 100% | 單位: 線性
            </div>
            <!-- 波形顯示區域帶縱軸 -->
            <div class="waveform-with-axis">
                <div class="waveform-container">
                    <div class="file-info" id="fileInfo">
                        <!-- 文件信息會顯示在這裡 -->
                    </div>
                    
                    <canvas class="waveform-canvas" id="waveformCanvas"></canvas>
                    <canvas class="spectrogram-canvas" id="spectrogramCanvas" style="display: none;"></canvas>

                    <div class="crosshair" id="crosshair" style="display: none;">
                        <div class="crosshair-h"></div>
                        <div class="crosshair-v"></div>
                    </div>

                    <!-- Sample值显示 -->
                    <div class="sample-tooltip" id="sampleTooltip" style="display: none;">
                        <span id="sampleValue"></span>
                    </div>

                    <!-- 頻譜圖信息顯示 -->
                    <div class="spectrogram-tooltip" id="spectrogramTooltip" style="display: none;">
                        <span id="spectrogramValue"></span>
                    </div>
                    
                    <div class="playhead" id="playhead" style="left: 0;"></div>
                    <div class="selection-box" id="selectionBox" style="display: none;"></div>
                </div>
                

                <!-- 右側縱軸 -->
                <div class="amplitude-axis" id="amplitudeAxis">
                 <!-- 振幅標籤會動態生成 -->
                </div>

                </div>

        </div>

        <!-- 時間軸 -->
        <div class="timeline" id="timeline"></div>

        <!-- 調整手柄 -->
        <div class="resize-handle" id="resizeHandle"></div>
        <!-- 底部面板 -->
        <div class="bottom-panel">
            <!-- 頻譜分析 -->
            <div class="spectrum-panel">
                <div class="frequency-labels">
                    <div>8kHz</div>
                    <div>6kHz</div>
                    <div>4kHz</div>
                    <div>2kHz</div>
                    <div>1kHz</div>
                    <div>500Hz</div>
                    <div>0Hz</div>
                </div>
                <canvas class="spectrum-canvas" id="spectrumCanvas"></canvas>
            </div>
            
            <!-- 音量表和統計 -->
            <div class="meters-panel">
                <div class="meter-group">
                    <div class="meter-title">音量 (dB)</div>
                    <div class="level-meter">
                        <div class="level-bar" id="volumeBar"></div>
                        <div class="level-text" id="volumeText">-∞</div>
                    </div>
                </div>
                
                <div class="meter-group">
                    <div class="meter-title">峰值 (dB)</div>
                    <div class="level-meter">
                        <div class="level-bar" id="peakBar"></div>
                        <div class="level-text" id="peakText">-∞</div>
                    </div>
                </div>
                
                <div class="meter-group">
                    <div class="meter-title">統計</div>
                    <div style="font-size: 10px; line-height: 1.4;">
                        <div>採樣率: <span id="sampleRateDisplay">--</span></div>
                        <div>時長: <span id="durationDisplay">--</span></div>
                        <div>聲道: <span id="channelsDisplay">--</span></div>
                        <div>質量: <span id="quality">--</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日誌區域 -->
        <div class="log" id="log">
            <strong>📝 操作日誌：</strong><br>
            等待WAV文件拖拽...
        </div>
    <!-- 右键菜单背景遮罩 -->
    <div class="context-menu-overlay" id="contextMenuOverlay"></div>
    
    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" data-action="cut">clipboard</div>
        <div class="context-menu-item" data-action="copy">copy</div>
        <div class="context-menu-item" data-action="paste" id="pasteItem">pasteFromClipboard</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item has-submenu" data-action="volume">
            声音调整
            <div class="context-submenu">
                <div class="context-menu-item" data-action="amplify-150">放大 150%</div>
                <div class="context-menu-item" data-action="amplify-200">放大 200%</div>
                <div class="context-menu-item" data-action="reduce-50">缩小 50%</div>
                <div class="context-menu-item" data-action="reduce-25">缩小 25%</div>
                <div class="context-menu-item" data-action="normalize">标准化</div>
            </div>
        </div>
    </div>

    </div>

    <script>
        class ProfessionalWavAnalyzer {
            // 在現有 ProfessionalWavAnalyzer 類中添加選單處理方法

            setupMenuSystem() {
                // 選單項點擊事件
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleMenu(item);
                    });

                    item.addEventListener('mouseenter', () => {
                        if (this.isMenuOpen) {
                            this.openMenu(item);
                        }
                    });
                });

                // 下拉選單項點擊事件
                document.querySelectorAll('.dropdown-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.stopPropagation();
                        if (!item.classList.contains('disabled')) {
                            const action = item.dataset.action;
                            if (action) {
                                this.executeMenuAction(action);
                                this.closeAllMenus();
                            }
                        }
                    });
                });

                // 點擊其他地方關閉選單
                document.addEventListener('click', () => {
                    this.closeAllMenus();
                });
            }

            toggleMenu(menuItem) {
                if (this.activeMenu === menuItem) {
                    this.closeAllMenus();
                } else {
                    this.openMenu(menuItem);
                }
            }

            openMenu(menuItem) {
                this.closeAllMenus();
                this.activeMenu = menuItem;
                this.isMenuOpen = true;

                menuItem.classList.add('active');
                const dropdown = menuItem.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.classList.add('show');
                }
            }

            closeAllMenus() {
                this.isMenuOpen = false;
                this.activeMenu = null;

                // 拖拽覆盖层相关变量
                this.dragOverlay = null;   
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.classList.remove('active');
                });

                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }

            executeMenuAction(action) {
                switch(action) {
                    case 'open':
                        // 觸發檔案選擇
                        this.selectNewFile();
                        break;
                    case 'select-all':
                        if (this.audioData) this.selectAll();
                        break;
                    case 'zoom-in':
                        this.zoomIn();
                        break;
                    case 'zoom-out':
                        this.zoomOut();
                        break;
                    case 'zoom-fit':
                        if (this.audioData) this.autoFitWaveform();
                        break;
                    case 'toggle-spectrum':
                        document.getElementById('showBottomPanel').click();
                        break;
                    case 'about':
                        alert('專業WAV文件分析器 v2.0\n\n具備高精度波形顯示和頻譜分析功能\n\n使用說明：\n- 拖拽WAV文件到任意區域載入\n- Ctrl+雙擊可重新選擇文件\n- 支持無限重載入功能');
                        break;
                    default:
                        this.log(`選單操作: ${action}`);
                        break;
                }
            }
            

            constructor() {
                // 基本狀態
                this.audioData = null;
                this.sampleRate = 44100;
                this.totalDuration = 0;
                this.displayBuffer = new Float32Array(8192);
                this.spectrumData = new Uint8Array(1024);

                // 确保pixelsPerSecond在最开始就被正确初始化
                this.pixelsPerSecond = 100;

                // Web Audio API 播放相關
                this.audioContext = null;
                this.audioBuffer = null;
                this.audioSource = null;
                this.isPlaying = false;
                this.startTime = 0;
                this.pauseTime = 0;
                this.currentPlayTime = 0;

                // 播放起始位置记录
                this.lastPlayPosition = 0; // 记录最后的播放位置

                // 固定階數縮放系統
                this.zoomSteps = [
                    0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000,
                    2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000, 1000000
                ];
                this.currentZoomIndex = 3; // 默认从1开始
                this.zoomLevel = this.zoomSteps[this.currentZoomIndex]; // 确保zoomLevel有初始值
                this.sampleLevelThreshold = 50; // 降低样本显示阈值
                this.sampleLevelThreshold = 1000; // 當縮放超過此值時顯示樣本點
                this.amplitudeZoom = 10;
                this.maxAmplitudeZoom = 1000; // 大幅提高振幅缩放上限
                this.amplitudeUnit = 'linear';
                this.amplitudeOffset = 0; // 纵轴偏移量

                // 重新计算pixelsPerSecond基于当前zoomLevel
                this.updatePixelsPerSecond();
                // 選擇
                this.selectionStart = 0;
                this.selectionEnd = 0;

                // 滑鼠狀態
                this.isCtrlPressed = false;
                this.isShiftPressed = false;
                this.justFinishedSelection = false; // 防止选择完成后立即被点击清除

                // 視角跟隨
                this.viewOffset = 0;  // 當前視角偏移（像素）
                this.maxViewOffset = 0;  // 最大可偏移量

                // 縮放步進設定
                this.timeZoomStep = 10;  // 時間軸縮放步進值
                this.timeZoomStepLarge = 50; // 高縮放級別時的步進值


                // 自动适配缩放相关
                this.autoFitZoomIndex = 3; // 记录自动适配时的缩放级别
                this.hasAutoFitted = false; // 标记是否已经执行过自动适配
                this.minAllowedZoomIndex = 0; // 允许的最小缩放级别索引
                this.defaultLoadZoomIndex = 3; // 载入时的默认缩放级别索引
                this.dynamicMinZoomIndex = 0; // 根据音频长度动态计算的最小缩放级别

                // 布局调整
                this.isResizingHeight = false;

                // 菜单系统状态
                this.activeMenu = null;
                this.isMenuOpen = false;

                // 菜單系統狀態
                this.activeMenu = null;
                this.isMenuOpen = false;

            
                // 右键菜单和剪贴板
                this.clipboard = null;
                this.contextMenu = null;

                // 顯示模式
                this.displayMode = 'waveform'; // 'waveform' 或 'spectrogram'
                this.spectrogramData = null;
                this.fftSize = 1024; // 減小FFT大小以提高速度
                this.hopSize = 256;  // 減小hop size

                // 初始化
                this.setupCanvases();
                this.setupDropZone();
                this.setupEventListeners();
                this.setupWaveformInteraction();
                this.setupResizeHandle();
                this.setupTimeInfoInteraction();
                this.setupAmplitudeAxisInteraction();
                this.setupMenuSystem();
                this.setupHeightAdjuster();
                this.updateAmplitudeAxis();
                this.setupContextMenu();
                this.setupBottomPanelToggle();
                this.startAnimationLoop();
                this.setupPlayheadInteraction();
                this.updateTimeline();
                this.updateWaveformInfo();
                this.updateAmplitudeAxis();
                this.log('🎵 專業WAV分析器已啟動');
                this.log('📁 請拖拽WAV文件到藍色框內');
            }
            // 新增方法：更新pixelsPerSecond
            updatePixelsPerSecond() {
                this.pixelsPerSecond = this.zoomLevel * 10;
            }

            // 根据音频长度动态计算最小缩放级别
            calculateDynamicMinZoom() {
                if (!this.totalDuration || !this.canvas) {
                    return this.defaultLoadZoomIndex;
                }

                const containerWidth = this.canvas.clientWidth;
                if (containerWidth <= 0) {
                    return this.defaultLoadZoomIndex;
                }

                // 计算能完整显示音频所需的最小像素每秒
                const minPixelsPerSecond = containerWidth / this.totalDuration;
                const minZoomLevel = minPixelsPerSecond / 10; // 因为 pixelsPerSecond = zoomLevel * 10

                // 找到最接近的缩放级别索引
                let minIndex = 0;
                for (let i = 0; i < this.zoomSteps.length; i++) {
                    if (this.zoomSteps[i] >= minZoomLevel) {
                        minIndex = i;
                        break;
                    }
                }

                // 确保不小于绝对最小值（索引0）
                this.dynamicMinZoomIndex = Math.max(0, minIndex);

                return this.dynamicMinZoomIndex;
            }
            async initAudioContext() {
                if (!this.audioContext) {
                    try {
                        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        this.log('🔊 音頻上下文初始化成功');
                    } catch (error) {
                        this.log('❌ 音頻上下文初始化失敗: ' + error.message);
                        throw error;
                    }
                }
            }
            setupCanvases() {
                this.canvas = document.getElementById('waveformCanvas');
                this.spectrumCanvas = document.getElementById('spectrumCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.spectrumCtx = this.spectrumCanvas.getContext('2d');
                
                // 設置canvas大小
                [this.canvas, this.spectrumCanvas].forEach(canvas => {
                    const rect = canvas.getBoundingClientRect();
                    canvas.width = rect.width * window.devicePixelRatio;
                    canvas.height = rect.height * window.devicePixelRatio;
                    const ctx = canvas.getContext('2d');
                    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
                });
                
                this.drawPlaceholder();
            }
            resizeCanvases() {
                // 強制重新計算canvas尺寸
                setTimeout(() => {
                    this.setupCanvases();
                    this.updateAmplitudeAxis();
                }, 100);
            }
            setupDropZone() {
                // 改为使用整个主区域作为拖拽区域
                const container = document.querySelector('.main-area');
                const dropZone = document.getElementById('dropZone');
                const waveformContainer = document.querySelector('.waveform-container');

                // 防止默認拖拽行為 - 应用到整个主区域
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    container.addEventListener(eventName, (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    });
                    
                    document.addEventListener(eventName, (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    });
                });
                

                // 拖拽效果 - 在整个主区域
                container.addEventListener('dragenter', (e) => {
                    // 显示拖拽提示
                    this.showDragOverlay();
                    this.log('🔵 檢測到文件拖拽進入');
                });
                
                container.addEventListener('dragover', (e) => {
                    this.showDragOverlay();
                });
                

                container.addEventListener('dragleave', (e) => {
                    // 检查是否真的离开了主区域
                    if (!container.contains(e.relatedTarget)) {
                        this.hideDragOverlay();
                        this.log('🔴 文件拖拽離開');
                    }
                });
                

                // 文件釋放 - 在整个主区域
                container.addEventListener('drop', (e) => {
                    this.hideDragOverlay();
                    this.log('📥 文件釋放事件觸發');
                    
                    const files = Array.from(e.dataTransfer.files);
                    this.log(`📂 檢測到 ${files.length} 個文件`);
                    
                    if (files.length > 0) {
                        const file = files[0];
                        this.log(`文件名: ${file.name}, 大小: ${file.size} 字節, 類型: ${file.type}`);
                        this.processFile(file);
                    }
                });
                

                    // 点击选择文件 - 可以点击任何地方
                    const clickToSelect = () => {
                        input.type = 'file';
                        input.accept = '.wav,audio/wav,audio/x-wav';
                        input.onchange = (e) => {
                            if (e.target.files.length > 0) {
                                this.log(`通過點擊選擇了文件: ${e.target.files[0].name}`);
                                this.processFile(e.target.files[0]);
                            }
                        };
                        input.click();             
                    };
                
                // 初始状态的点击事件
                dropZone.addEventListener('click', clickToSelect);
                
               // 如果已经加载了文件，在波形区域双击也能重新选择
                waveformContainer.addEventListener('dblclick', (e) => {
                    // 只有在按住Ctrl时才触发文件选择，避免与其他双击功能冲突
                    if (e.ctrlKey || e.metaKey) {
                        e.stopPropagation();
                        clickToSelect();
                        this.log('🔄 Ctrl+雙擊重新選擇文件');
                    }
                });
            }
            
            // 显示拖拽覆盖层
            showDragOverlay() {
                if (!this.dragOverlay) {
                    this.createDragOverlay();
                }
                this.dragOverlay.style.display = 'flex';
            }
            
            // 隐藏拖拽覆盖层
            hideDragOverlay() {
                if (this.dragOverlay) {
                    this.dragOverlay.style.display = 'none';
                }
            }
            
            // 创建拖拽覆盖层
            createDragOverlay() {
                this.dragOverlay = document.createElement('div');
                this.dragOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 123, 204, 0.1);
                    border: 3px dashed #007acc;
                    display: none;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    pointer-events: none;
                    backdrop-filter: blur(2px);
                `;
                
                const icon = document.createElement('div');
                icon.style.cssText = `
                    font-size: 48px;
                    margin-bottom: 20px;
                    color: #007acc;
                `;
                icon.textContent = '📁';
                
                const text = document.createElement('div');
                text.style.cssText = `
                    font-size: 24px;
                    color: #007acc;
                    font-weight: bold;
                    text-align: center;
                    margin-bottom: 10px;
                `;
                text.textContent = this.audioData ? '拖拽新的WAV文件以重新載入' : '釋放以載入WAV文件';
                
                const subtext = document.createElement('div');
                subtext.style.cssText = `
                    font-size: 16px;
                    color: #005c99;
                    text-align: center;
                `;
                subtext.textContent = this.audioData ? '支持替換當前文件' : '支持WAV格式文件';
                
                this.dragOverlay.appendChild(icon);
                this.dragOverlay.appendChild(text);
                this.dragOverlay.appendChild(subtext);
                
                const mainArea = document.querySelector('.main-area');
                mainArea.appendChild(this.dragOverlay);
            }           
            setupEventListeners() {
                // 工具欄按鈕
                document.getElementById('connectBtn').addEventListener('click', () => this.connectRTP());
                document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
                document.getElementById('playBtn').addEventListener('click', () => this.playAudio());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopAudio());
                
                // 縮放控制
                document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
                document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());

              
                // 设置滑块限制
                this.setupZoomSliderLimits();

                document.getElementById('zoomSlider').addEventListener('input', (e) => {
                    const targetZoom = parseFloat(e.target.value);
                    // 计算动态最小值
                    const dynamicMinIndex = this.calculateDynamicMinZoom();
                    const dynamicMinZoom = this.zoomSteps[dynamicMinIndex];

                    // 确保滑块值不低于动态最小值
                    if (targetZoom < dynamicMinZoom) {
                        e.target.value = dynamicMinZoom;
                        return;
                    }
                    this.setZoomLevel(targetZoom);
                });
                
                // 振幅控制
                document.getElementById('ampZoomIn').addEventListener('click', () => this.amplitudeZoomIn());
                document.getElementById('ampZoomOut').addEventListener('click', () => this.amplitudeZoomOut());
                document.getElementById('ampSlider').addEventListener('input', (e) => {
                    this.amplitudeZoom = parseInt(e.target.value);
                    this.updateWaveformInfo();
                });
                document.getElementById('ampUnit').addEventListener('change', (e) => {
                    this.amplitudeUnit = e.target.value;
                    this.updateWaveformInfo();
                    this.updateAmplitudeAxis();
                });
                
                // 縮放步進控制
                document.getElementById('zoomStep').addEventListener('input', (e) => {
                    const step = parseInt(e.target.value);
                    this.timeZoomStep = Math.max(1, Math.min(100, step));
                    this.timeZoomStepLarge = this.timeZoomStep * 5; // 大步進是小步進的5倍
                    this.log(`縮放步進設定為: ${this.timeZoomStep}倍`);
                 });

                // 顯示模式切換
                document.getElementById('waveformModeBtn').addEventListener('click', () => {
                    this.setDisplayMode('waveform');
                });
                document.getElementById('spectrogramModeBtn').addEventListener('click', () => {
                    this.setDisplayMode('spectrogram');
                });
                // 鍵盤事件
                document.addEventListener('keydown', (e) => {
                    this.isCtrlPressed = e.ctrlKey || e.metaKey;
                    this.isShiftPressed = e.shiftKey;
                });
                
                document.addEventListener('keyup', (e) => {
                    this.isCtrlPressed = e.ctrlKey || e.metaKey;
                    this.isShiftPressed = e.shiftKey;
                });
                
                // 窗口調整
                window.addEventListener('resize', () => this.setupCanvases());
                 window.addEventListener('resize', () => this.resizeCanvases());
                
                // 定期检查缩放限制（防止意外的缩放越界）
                setInterval(() => {
                    this.enforceZoomLimits();
                }, 1000);
            }
            setupBottomPanelToggle() {
                const checkbox = document.getElementById('showBottomPanel');
                const bottomPanel = document.querySelector('.bottom-panel');
                
                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        bottomPanel.classList.remove('hidden');
                    } else {
                        bottomPanel.classList.add('hidden');
                    }
                });
            }

            setupTimeInfoInteraction() {
               const timeInfoArea = document.getElementById('timeline');
               let isDraggingTimeView = false;
               let lastMouseX = 0;
               
               timeInfoArea.addEventListener('mouseenter', () => {
                     if (!isDraggingTimeView) {
                        timeInfoArea.style.cursor = 'grab';
                    }
               });
               
               timeInfoArea.addEventListener('mouseleave', () => {
                if (!isDraggingTimeView) {
                       timeInfoArea.style.cursor = 'default';
                   }
               });
               
               timeInfoArea.addEventListener('mousedown', (e) => {
                   if (!this.audioData) return;
                   if (e.button !== 0) return; // 只響應左鍵
                   isDraggingTimeView = true;
                   lastMouseX = e.clientX;
                   timeInfoArea.style.cursor = 'grabbing';
                   e.preventDefault();
                   e.stopPropagation();
               });
               
               document.addEventListener('mousemove', (e) => {
                   if (!isDraggingTimeView || !this.audioData) return;

                   const deltaX = e.clientX - lastMouseX;
                   this.viewOffset -= deltaX;
                   this.updateViewOffset();
                   this.clampViewToAudioBounds();
                   this.updateTimeline(); // 實時更新時間軸

                   // 平移时更新选择框位置，使其保持在对应的音频时间位置
                   this.restoreSelectionState();

                   lastMouseX = e.clientX;
               });
               
               document.addEventListener('mouseup', () => {
                   if (isDraggingTimeView) {
                       isDraggingTimeView = false;
                       const timeInfoArea = document.getElementById('timeline');

                        if (timeInfoArea.matches(':hover')) {
                            timeInfoArea.style.cursor = 'grab';
                        } else {
                            timeInfoArea.style.cursor = 'default';
                        }
                   }
               });
               
               // 時間軸區域的滾輪縮放
               timeInfoArea.addEventListener('wheel', (e) => {
                   this.handleTimeAxisZoom(e);
               });
               // 防止文字選擇
               timeInfoArea.addEventListener('selectstart', (e) => {
                    e.preventDefault();
                });
                // 添加調試日誌
                timeInfoArea.addEventListener('mouseenter', () => {
                    console.log('Mouse entered timeinfo area');
                });
                timeInfoArea.addEventListener('mousedown', () => {
                    console.log('Mouse down in timeinfo area');
                });
           }
            setupWaveformInteraction() {
                const container = document.querySelector('.waveform-container');
                const crosshair = document.getElementById('crosshair');
                const selectionBox = document.getElementById('selectionBox');

                // 监控选择框显示状态变化
                let lastSelectionBoxDisplay = selectionBox.style.display;
                const monitorSelectionBox = () => {
                    const currentDisplay = selectionBox.style.display;
                    if (currentDisplay !== lastSelectionBoxDisplay) {
                        this.log(`🔍 选择框显示状态变化: ${lastSelectionBoxDisplay} → ${currentDisplay}`);
                        lastSelectionBoxDisplay = currentDisplay;
                    }
                };
                setInterval(monitorSelectionBox, 100); // 每100ms检查一次
                
                let mouseDownX = 0;
                let isDragging = false;
                let isDraggingView = false;
                let lastMouseX = 0;
                
                container.addEventListener('mouseenter', () => {
                    if (this.audioData) crosshair.style.display = 'block';
                });
                
                container.addEventListener('mouseleave', () => {
                    crosshair.style.display = 'none';
                    document.getElementById('sampleTooltip').style.display = 'none';
                });
                
                // 保存鼠标状态，用于右键菜单判断
               this.isRightClickActive = false;
               this.lastSelectionState = null;
                container.addEventListener('mousemove', (e) => {
                    if (!this.audioData) return;

                    const rect = container.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    // 更新十字线位置
                    crosshair.style.left = mouseX + 'px';
                    crosshair.style.top = mouseY + 'px';

                    // 检查是否在sample级别并显示sample值
                    this.updateSampleTooltip(mouseX, mouseY);

                    if (isDraggingView) {
                        const deltaX = mouseX - lastMouseX;
                        this.viewOffset -= deltaX;
                        this.updateViewOffset();
                        // 平移时更新选择框位置，使其保持在对应的音频时间位置
                        this.restoreSelectionState();
                        lastMouseX = mouseX;
                    }

                    if (isDragging && !isDraggingView) {
                        const width = Math.abs(mouseX - mouseDownX);
                        const left = Math.min(mouseX, mouseDownX);

                        selectionBox.style.left = left + 'px';
                        selectionBox.style.width = width + 'px';
                        selectionBox.style.top = '0';
                        selectionBox.style.height = '100%';
                        selectionBox.style.display = 'block'; // 确保选择框可见

                        this.updateSelection(left, left + width);

                        // 调试信息（只在宽度大于1px时显示）
                        if (width > 1) {
                            this.log(`🖱️ 框选中: 左=${left}px, 宽=${width}px, 显示=${selectionBox.style.display}, 时间=${this.selectionStart.toFixed(3)}s-${this.selectionEnd.toFixed(3)}s`);
                        }
                    } else if (isDragging) {
                        this.log(`⚠️ isDragging=${isDragging}, isDraggingView=${isDraggingView} - 选择被阻止`);
                    }
                });
                
                container.addEventListener('mousedown', (e) => {
                    this.log(`🖱️ 鼠标按下事件: button=${e.button}, ctrlKey=${e.ctrlKey}, audioData=${!!this.audioData}`);

                    if (!this.audioData) {
                        this.log(`❌ 没有音频数据，忽略鼠标事件`);
                        return;
                    }

                    // 检测右键
                    if (e.button === 2) {
                        this.isRightClickActive = true;
                        this.log(`🖱️ 右键按下`);
                        return; // 右键时不开始新的选择
                    }

                    this.isRightClickActive = false;
                    const rect = container.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    mouseDownX = mouseX;
                    lastMouseX = mouseX;

                    this.log(`🖱️ 鼠标位置: ${mouseX}px, isCtrlPressed=${this.isCtrlPressed}`);

                    if (e.button === 1 || (e.button === 0 && this.isCtrlPressed)) {
                        // 中键或Ctrl+左键：拖拽视图
                        isDraggingView = true;
                        e.preventDefault();
                        this.log(`🖱️ 开始视图拖拽模式`);
                    } else if (e.button === 0) {
                        // 普通左键：开始框选
                        isDragging = true;
                        selectionBox.style.display = 'block';
                        selectionBox.style.left = mouseDownX + 'px';
                        selectionBox.style.width = '0px';
                        selectionBox.style.top = '0';
                        selectionBox.style.height = '100%';
                        selectionBox.style.border = '2px solid #ffff00';
                        selectionBox.style.background = 'rgba(255, 255, 0, 0.2)';
                        this.log(`🖱️ 开始框选模式，起始位置: ${mouseDownX}px, isDragging=${isDragging}, 选择框显示=${selectionBox.style.display}`);
                    }
                });
                
                container.addEventListener('mouseup', (e) => {
                    if (isDragging && !isDraggingView) {
                        // 框选完成
                        const duration = this.selectionEnd - this.selectionStart;
                        this.log(`✅ 框选完成: ${this.formatTime(this.selectionStart)} - ${this.formatTime(this.selectionEnd)} (${this.formatTime(duration)})`);

                        // 左键释放后保存选择状态
                        if (e.button === 0) {
                            this.saveSelectionState();
                            this.log(`💾 左键释放，保存选择状态`);

                            // 设置标志防止点击事件立即清除选择
                            this.justFinishedSelection = true;
                            setTimeout(() => {
                                this.justFinishedSelection = false;
                                this.log(`🔓 清除选择完成标志，允许点击清除选择`);
                            }, 100); // 100ms内不允许点击清除选择

                            // 延迟确保选择框在释放后保持可见（防止其他事件隐藏它）
                            setTimeout(() => {
                                const selectionBox = document.getElementById('selectionBox');
                                if (selectionBox && this.selectionEnd > this.selectionStart) {
                                    selectionBox.style.display = 'block';
                                    selectionBox.style.border = '2px solid #ffff00';
                                    selectionBox.style.background = 'rgba(255, 255, 0, 0.2)';
                                    this.log(`👁️ 延迟确保选择框保持可见: ${selectionBox.style.display}`);
                                }
                            }, 10); // 10ms延迟
                        }
                    }

                    isDraggingView = false;
                    isDragging = false;
                    this.isRightClickActive = false;
                });
                
                // 滾輪縮放
                container.addEventListener('wheel', (e) => { 
                    if (this.isCtrlPressed) {
                        // Ctrl+滾輪：振幅縮放
                        this.handleAmplitudeZoom(e);
                    } else {
                        // 普通滾輪：時間軸縮放 - 以鼠标为中心
                        this.handleWaveformTimeZoom(e);
                    }
                });
                
               // 雙擊功能：默认全选，Ctrl+双击选择新文件
                container.addEventListener('dblclick', (e) => {
                    if (!this.audioData) return;
                   
                    if (e.ctrlKey || e.metaKey) {
                        // Ctrl+双击：选择新文件
                        e.stopPropagation();
                        this.selectNewFile();
                    } else {
                        // 普通双击：全选
                        this.selectAll();
                    }
                });
            }
            
            // 选择新文件的方法
            selectNewFile() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.wav,audio/wav,audio/x-wav';
                input.onchange = (e) => {
                    if (e.target.files.length > 0) {
                        this.log(`🔄 Ctrl+雙擊選擇新文件: ${e.target.files[0].name}`);
                        this.processFile(e.target.files[0]);
                    }
                };
                input.click();
            }
           // 修复双击事件处理，确保不影响其他功能
            fixDoubleClickHandler() {
                const container = document.querySelector('.waveform-container');
                // 移除现有的双击监听器
                const newContainer = container.cloneNode(true);
                container.parentNode.replaceChild(newContainer, container);
                // 重新设置交互
                this.setupWaveformInteraction();
            }
           // 保存选择状态
           saveSelectionState() {
               const selectionBox = document.getElementById('selectionBox');
               this.lastSelectionState = {
                   display: selectionBox.style.display,
                   left: selectionBox.style.left,
                   width: selectionBox.style.width,
                   top: selectionBox.style.top,
                   height: selectionBox.style.height,
                   selectionStart: this.selectionStart,
                   selectionEnd: this.selectionEnd
               };
               this.log(`💾 保存选择状态: display=${selectionBox.style.display}, 时间=${this.selectionStart.toFixed(3)}s-${this.selectionEnd.toFixed(3)}s`);
           }

           // 时间转换为像素位置（全局坐标）
           timeToPixel(time) {
               return time * this.pixelsPerSecond;
           }

           // 验证选择框时间一致性的调试函数
           verifySelectionTimeConsistency() {
               if (this.selectionStart === 0 && this.selectionEnd === 0) return;

               const selectionBox = document.getElementById('selectionBox');
               if (selectionBox.style.display === 'none') return;

               const { startTime } = this.getVisibleTimeRange();
               const left = parseFloat(selectionBox.style.left);
               const width = parseFloat(selectionBox.style.width);

               // 根据当前显示位置反推时间
               const calculatedStartTime = startTime + (left / this.pixelsPerSecond);
               const calculatedDuration = width / this.pixelsPerSecond;
               const calculatedEndTime = calculatedStartTime + calculatedDuration;

               const startTimeDiff = Math.abs(calculatedStartTime - this.selectionStart);
               const endTimeDiff = Math.abs(calculatedEndTime - this.selectionEnd);

               if (startTimeDiff > 0.001 || endTimeDiff > 0.001) {
                   this.log(`⚠️ 选择框时间不一致！`);
                   this.log(`   存储时间: ${this.selectionStart.toFixed(3)}s - ${this.selectionEnd.toFixed(3)}s`);
                   this.log(`   计算时间: ${calculatedStartTime.toFixed(3)}s - ${calculatedEndTime.toFixed(3)}s`);
                   this.log(`   差异: 开始${startTimeDiff.toFixed(3)}s, 结束${endTimeDiff.toFixed(3)}s`);
               } else {
                   this.log(`✅ 选择框时间一致: ${this.selectionStart.toFixed(3)}s - ${this.selectionEnd.toFixed(3)}s`);
               }
           }

           // 恢复选择状态
           restoreSelectionState() {
               if (this.lastSelectionState && this.lastSelectionState.display === 'block') {
                   const selectionBox = document.getElementById('selectionBox');

                   // 恢复时间选择值
                   this.selectionStart = this.lastSelectionState.selectionStart;
                   this.selectionEnd = this.lastSelectionState.selectionEnd;

                   // 根据当前缩放级别重新计算选择框的像素位置
                   if (this.selectionStart !== this.selectionEnd) {
                       // 计算选择区域在当前视图中的相对位置
                       const { startTime } = this.getVisibleTimeRange();
                       const startRelativeTime = this.selectionStart - startTime;
                       const endRelativeTime = this.selectionEnd - startTime;
                       const left = startRelativeTime * this.pixelsPerSecond;
                       const width = (this.selectionEnd - this.selectionStart) * this.pixelsPerSecond;

                       // 更新选择框的视觉位置
                       selectionBox.style.display = 'block';
                       selectionBox.style.left = `${left}px`;
                       selectionBox.style.width = `${width}px`;
                       selectionBox.style.top = this.lastSelectionState.top;
                       selectionBox.style.height = this.lastSelectionState.height;

                       this.log(`🔄 恢复选择框: 时间${this.selectionStart.toFixed(3)}s-${this.selectionEnd.toFixed(3)}s, 位置${left.toFixed(1)}px, 宽度${width.toFixed(1)}px, 缩放${this.pixelsPerSecond.toFixed(1)}px/s`);
                   } else {
                       // 如果没有有效选择，隐藏选择框
                       selectionBox.style.display = 'none';
                   }
               }
           }
            handleWaveformTimeZoom(e) {
               if (!this.audioData) return;
               e.preventDefault();

               const container = e.currentTarget;
               const rect = container.getBoundingClientRect();
               const mouseX = e.clientX - rect.left;
               const delta = e.deltaY > 0 ? -1 : 1;

               // 简化调试信息（仅在需要时输出）
               const debugZoom = false; // 设为 true 启用详细调试
               if (debugZoom) {
                   this.log(`🖱️ 滚轮事件详情: clientX=${e.clientX}, rect.left=${rect.left}, mouseX=${mouseX}`);
                   this.log(`📦 容器信息: width=${rect.width}, height=${rect.height}`);
               }

               // 确保pixelsPerSecond有效
                if (isNaN(this.pixelsPerSecond) || this.pixelsPerSecond <= 0) {
                    this.updatePixelsPerSecond();
                }

               // 保存选择状态
               this.saveSelectionState();

               // 记录缩放前的状态
               const oldZoom = this.zoomLevel;
               const oldPixelsPerSecond = this.pixelsPerSecond;

               // 计算鼠标位置的时间点（考虑当前视图偏移）
               const { startTime } = this.getVisibleTimeRange();
               const mouseRelativeTime = mouseX / oldPixelsPerSecond;
               const mouseAbsoluteTime = startTime + mouseRelativeTime;

               if (debugZoom) {
                   this.log(`🖱️ 滚轮缩放: 鼠标位置=${mouseX}px, 对应时间=${mouseAbsoluteTime.toFixed(3)}s, 方向=${delta > 0 ? '放大' : '缩小'}`);
                   this.log(`📊 缩放前状态: viewOffset=${this.viewOffset.toFixed(1)}px, startTime=${startTime.toFixed(3)}s, pixelsPerSecond=${oldPixelsPerSecond.toFixed(1)}`);
               }

               // 执行固定阶数缩放
               const oldIndex = this.currentZoomIndex;

               if (delta > 0 && this.currentZoomIndex < this.zoomSteps.length - 1) {
                   // 放大：检查是否超过最大值
                   this.currentZoomIndex++;
               } else if (delta < 0) {
                   // 缩小：检查动态最小缩放限制
                   const dynamicMinIndex = this.calculateDynamicMinZoom();
                   if (this.currentZoomIndex > dynamicMinIndex) {
                       this.currentZoomIndex--;
                   } else {
                       // 已达到动态最小缩放级别，不允许继续缩小
                       return; // 直接返回，不执行后续的缩放操作
                   }
               }

               // 只有当缩放级别真正改变时才更新
                if (oldIndex !== this.currentZoomIndex) {
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                }

               const newPixelsPerSecond = this.pixelsPerSecond;

               // 只有当缩放真正改变时才调整视角
                if (oldIndex !== this.currentZoomIndex && !isNaN(mouseAbsoluteTime)) {
                    // 计算新的视角偏移，使鼠标位置的时间点保持在相同的屏幕位置
                    const newMouseGlobalX = mouseAbsoluteTime * newPixelsPerSecond;
                    const newViewOffset = newMouseGlobalX - mouseX;

                    this.viewOffset = newViewOffset;
                    if (debugZoom) {
                        this.log(`🎯 调整视图偏移: ${this.viewOffset.toFixed(1)}px, 保持鼠标时间点${mouseAbsoluteTime.toFixed(3)}s在位置${mouseX}px`);
                        this.log(`📊 缩放后状态: newPixelsPerSecond=${newPixelsPerSecond.toFixed(1)}, newMouseGlobalX=${newMouseGlobalX.toFixed(1)}px`);
                    }
                }

               // 确保视角偏移在有效范围内
               this.updateViewOffset();
               this.clampViewToAudioBounds();

               // 更新UI（跳过自动视图调整，因为我们已经手动调整了）
               document.getElementById('zoomSlider').value = this.zoomLevel;
               this.updatePixelsPerSecond();
               this.updateViewOffset();
               this.clampViewToAudioBounds();
               this.updateTimeline();
               this.updateWaveformInfo();

               // 恢复选择状态
               this.restoreSelectionState();

               // 验证选择框时间一致性
               this.verifySelectionTimeConsistency();

               // 简化调试信息
               if (debugZoom && oldIndex !== this.currentZoomIndex) {
                    this.log(`🔍 波形缩放完成: ${oldZoom.toFixed(1)} → ${this.zoomLevel.toFixed(1)}, 鼠标时间: ${mouseAbsoluteTime.toFixed(3)}s, 鼠标位置: ${mouseX}px`);
                }
           }
                
           handleTimeAxisZoom(e) {
               if (!this.audioData) return;
               e.preventDefault();

               const rect = e.currentTarget.getBoundingClientRect();
               const mouseX = e.clientX - rect.left;
               const delta = e.deltaY > 0 ? -1 : 1;

               // 确保pixelsPerSecond有效
                if (isNaN(this.pixelsPerSecond) || this.pixelsPerSecond <= 0) {
                    this.updatePixelsPerSecond();
                }

               // 保存选择状态
               this.saveSelectionState();

               // 记录缩放前的状态
               const oldZoom = this.zoomLevel;
               const oldPixelsPerSecond = this.pixelsPerSecond;

               // 计算鼠标位置对应的时间点
               const { startTime } = this.getVisibleTimeRange();
               const mouseRelativeTime = mouseX / oldPixelsPerSecond;
               const mouseAbsoluteTime = startTime + mouseRelativeTime;

               if (debugZoom) {
                   this.log(`🖱️ 时间轴滚轮缩放: 鼠标位置=${mouseX}px, 对应时间=${mouseAbsoluteTime.toFixed(3)}s, 方向=${delta > 0 ? '放大' : '缩小'}`);
               }

               // 执行固定阶数缩放
               const oldIndex = this.currentZoomIndex;

               if (delta > 0 && this.currentZoomIndex < this.zoomSteps.length - 1) {
                   // 放大：检查是否超过最大值
                   this.currentZoomIndex++;
               } else if (delta < 0) {
                   // 缩小：检查动态最小缩放限制
                   const dynamicMinIndex = this.calculateDynamicMinZoom();
                   if (this.currentZoomIndex > dynamicMinIndex) {
                       this.currentZoomIndex--;
                   } else {
                       // 已达到动态最小缩放级别，不允许继续缩小
                       return; // 直接返回，不执行后续的缩放操作
                   }
               }

               // 只有当缩放级别真正改变时才更新
                if (oldIndex !== this.currentZoomIndex) {
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                }

               const newPixelsPerSecond = this.pixelsPerSecond;

               // 只有当缩放真正改变时才调整视角
                if (oldIndex !== this.currentZoomIndex && !isNaN(mouseAbsoluteTime)) {
                    // 计算新的视角偏移，使鼠标位置的时间点保持在相同的屏幕位置
                    const newMouseGlobalX = mouseAbsoluteTime * newPixelsPerSecond;
                    const newViewOffset = newMouseGlobalX - mouseX;

                    this.viewOffset = newViewOffset;
                    this.log(`🎯 时间轴调整视图偏移: ${this.viewOffset.toFixed(1)}px, 保持鼠标时间点${mouseAbsoluteTime.toFixed(3)}s在位置${mouseX}px`);
                }

               // 确保视角偏移在有效范围内
               this.updateViewOffset();
               this.clampViewToAudioBounds();

               // 更新UI（跳过自动视图调整，因为我们已经手动调整了）
               document.getElementById('zoomSlider').value = this.zoomLevel;
               this.updatePixelsPerSecond();
               this.updateViewOffset();
               this.clampViewToAudioBounds();
               this.updateTimeline();
               this.updateWaveformInfo();

               // 恢复选择状态
               this.restoreSelectionState();

               // 验证选择框时间一致性
               this.verifySelectionTimeConsistency();

               // 简化调试日志
               if (debugZoom && oldIndex !== this.currentZoomIndex) {
                    this.log(`🔍 时间轴缩放完成: ${oldZoom.toFixed(1)} → ${this.zoomLevel.toFixed(1)}, 鼠标时间: ${mouseAbsoluteTime.toFixed(3)}s, 鼠标位置: ${mouseX}px`);
               }

           }
           
           handleAmplitudeZoom(e) {
               if (!this.audioData) return;
               e.preventDefault();
               
               const delta = e.deltaY > 0 ? -1 : 1;
               
               // 振幅縮放
               this.amplitudeZoom = Math.max(1, Math.min(20, this.amplitudeZoom + delta));
               document.getElementById('ampSlider').value = this.amplitudeZoom;
               this.updateWaveformInfo();
               this.updateAmplitudeAxis();
           }

           // 更新sample tooltip显示
           updateSampleTooltip(mouseX, mouseY) {
               // 根據顯示模式選擇不同的tooltip處理
               if (this.displayMode === 'spectrogram') {
                   this.updateSpectrogramTooltip(mouseX, mouseY);
                   return;
               }

               const tooltip = document.getElementById('sampleTooltip');
               const sampleValue = document.getElementById('sampleValue');

               // 只在sample级别显示tooltip
               const showSamplePoints = this.zoomLevel > this.sampleLevelThreshold;
               if (!showSamplePoints || !this.audioData) {
                   tooltip.style.display = 'none';
                   return;
               }

               // 计算鼠标位置对应的sample
               const { startTime } = this.getVisibleTimeRange();
               const timeAtMouse = startTime + (mouseX / this.pixelsPerSecond);
               const sampleIndex = Math.round(timeAtMouse * this.sampleRate);

               // 检查sample索引是否有效
               if (sampleIndex < 0 || sampleIndex >= this.audioData.length) {
                   tooltip.style.display = 'none';
                   return;
               }

               // 计算sample在屏幕上的位置
               const sampleTime = sampleIndex / this.sampleRate;
               const sampleX = (sampleTime - startTime) * this.pixelsPerSecond;

               // 检查鼠标是否接近sample点（容差范围）
               const tolerance = Math.max(5, this.pixelsPerSecond / this.sampleRate * 2); // 至少5像素容差
               if (Math.abs(mouseX - sampleX) <= tolerance) {
                   // 获取sample值
                   const rawValue = this.audioData[sampleIndex];
                   let displayValue;

                   // 根据当前单位格式化显示值
                   switch (this.amplitudeUnit) {
                       case 'db':
                           if (Math.abs(rawValue) < 1e-10) {
                               displayValue = '-∞ dB';
                           } else {
                               // 使用与纵轴一致的dB范围映射：-60dB到+20dB
                               const dbValue = 20 * Math.log10(Math.abs(rawValue));
                               // 限制在纵轴显示范围内
                               const clampedDbValue = Math.max(-60, Math.min(20, dbValue));
                               displayValue = `${clampedDbValue.toFixed(2)} dB`;
                           }
                           break;
                       case 'percent':
                           displayValue = `${(rawValue * 100).toFixed(2)}%`;
                           break;
                       case 'linear':
                       default:
                           displayValue = Math.round(rawValue * 32768).toString();
                           break;
                   }

                   // 显示tooltip
                   sampleValue.textContent = `Sample ${sampleIndex}: ${displayValue}`;
                   tooltip.style.display = 'block';
                   tooltip.style.left = (sampleX + 10) + 'px'; // 在sample右上方
                   tooltip.style.top = (mouseY - 25) + 'px';
               } else {
                   tooltip.style.display = 'none';
               }
           }

           updateAmplitudeAxis() {
               const amplitudeAxis = document.getElementById('amplitudeAxis');
               if (!amplitudeAxis) return;

               // 如果是頻譜圖模式，顯示頻率軸
               if (this.displayMode === 'spectrogram') {
                   this.updateFrequencyAxis();
                   return;
               }

               // 应用纵轴偏移的视觉提示
               amplitudeAxis.style.backgroundColor = this.amplitudeOffset !== 0 ? '#8B7B7B' : '#7B7B7B';
                

               // 根据振幅缩放级别决定显示精度和刻度密度
               const zoomFactor = this.amplitudeZoom;
                let precision = 0;
                // 更精细的精度控制
                switch (this.amplitudeUnit) {
                    case 'db':
                        if (zoomFactor > 200) precision = 3;
                        else if (zoomFactor > 100) precision = 2;
                        else if (zoomFactor > 50) precision = 2;
                        else if (zoomFactor > 10) precision = 1;
                        else precision = 0;
                        break;
                    case 'percent':
                        if (zoomFactor > 200) precision = 3;
                        else if (zoomFactor > 100) precision = 2;
                        else if (zoomFactor > 50) precision = 2;
                        else if (zoomFactor > 15) precision = 1;
                        else precision = 0;
                        break;
                    case 'linear':
                    default:
                        if (zoomFactor > 500) precision = 1;
                        else if (zoomFactor > 100) precision = 1;
                        else precision = 0;
                        break;
                }
               
                 // 確保縱軸高度與波形容器一致
                 const waveformContainer = document.querySelector('.waveform-container');
                 if (waveformContainer) {
                     amplitudeAxis.style.height = waveformContainer.offsetHeight + 'px';
                 }
               amplitudeAxis.innerHTML = '';

               // 即使没有音频数据也显示默认刻度
               const showDefaultTicks = !this.audioData;
               
               const height = amplitudeAxis.offsetHeight || 400;

               
               // 根据缩放级别动态调整刻度密度
               let values = [];
               const densityLevel = Math.floor(Math.log10(zoomFactor)) + 1;
                                   
               // 优化精度计算
               switch (this.amplitudeUnit) {
                   case 'db':
                       precision = zoomFactor > 100 ? 2 : (zoomFactor > 50 ? 1 : 0);
                       break;
                   case 'percent':
                       precision = zoomFactor > 50 ? 1 : 0;
                       break;
                   case 'linear':
                   default:
                       precision = 0;
                       break;
               }
               switch (this.amplitudeUnit) {
                   case 'db':
                       if (zoomFactor > 1000) {
                            // 极高精度：每0.2dB一个刻度，确保足够间距
                            for (let db = -60; db <= 20; db += 0.2) {
                                values.push(Number(db.toFixed(1)));
                            }
                        } else if (zoomFactor > 500) {
                            // 超高精度：每0.5dB一个刻度
                            for (let db = -60; db <= 20; db += 0.5) {
                                values.push(Number(db.toFixed(1)));
                            }
                        } else if (zoomFactor > 200) {
                            // 高精度：每1dB一个刻度
                            for (let db = -60; db <= 20; db += 1) {
                                values.push(db);
                            }
                        } else if (zoomFactor > 100) {
                            // 中高精度：每2dB一个刻度
                            for (let db = -60; db <= 20; db += 2) {
                                values.push(db);
                            }
                        } else if (zoomFactor > 50) {
                            // 中精度：每3dB一个刻度
                            for (let db = -60; db <= 20; db += 3) {
                                values.push(db);
                            }
                        } else if (zoomFactor > 25) {
                            // 标准精度：每6dB一个刻度
                            for (let db = -60; db <= 20; db += 6) {
                                values.push(db);
                            }
                        } else if (zoomFactor > 15) {
                             values = [-60, -50, -40, -30, -20, -15, -10, -6, -3, -1, 0, 1, 3, 6, 10, 15, 20];
                        } else if (zoomFactor > 10) {
                             values = [-60, -40, -20, -10, -6, -3, -1, 0, 3, 6, 10, 20];
                         } else {
                             values = [-60, -20, -10, -6, -3, 0, 6, 10];
                         }
                       break;
                   case 'percent':
                       if (zoomFactor > 1000) {
                            // 极高精度：每2%一个刻度，确保足够间距
                            for (let pct = -100; pct <= 100; pct += 2) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 500) {
                            // 超高精度：每5%一个刻度
                            for (let pct = -100; pct <= 100; pct += 5) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 200) {
                            // 高精度：每10%一个刻度
                            for (let pct = -100; pct <= 100; pct += 10) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 100) {
                            // 中高精度：每12.5%一个刻度
                            for (let pct = -100; pct <= 100; pct += 12.5) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 50) {
                            // 中精度：每25%一个刻度
                            for (let pct = -100; pct <= 100; pct += 25) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 25) {
                            // 标准精度：每25%一个刻度
                            for (let pct = -100; pct <= 100; pct += 25) {
                                values.push(pct);
                            }
                        } else if (zoomFactor > 15) {
                             values = [-100, -87.5, -75, -62.5, -50, -37.5, -25, -12.5, 0, 12.5, 25, 37.5, 50, 62.5, 75, 87.5, 100];
                        } else if (zoomFactor > 10) {
                             values = [-100, -75, -50, -25, -12.5, 0, 12.5, 25, 50, 75, 100];
                         } else {
                             values = [-100, -50, -25, 0, 25, 50, 100];
                         }
                       break;
                   case 'linear':
                   default:
                        // 16位音频的范围，支持超高精度
                       if (zoomFactor > 1000) {
                            // 极高精度：每64个单位一个刻度
                            for (let val = -32768; val <= 32767; val += 64) {
                                values.push(val);
                            }
                        } else if (zoomFactor > 500) {
                            // 超高精度：每128个单位一个刻度
                            for (let val = -32768; val <= 32767; val += 128) {
                                values.push(val);
                            }
                        } else if (zoomFactor > 200) {
                            // 高精度：每256个单位一个刻度
                            for (let val = -32768; val <= 32767; val += 256) {
                                values.push(val);
                            }
                        } else if (zoomFactor > 100) {
                            // 中高精度：每512个单位一个刻度
                            for (let val = -32768; val <= 32767; val += 512) {
                                values.push(val);
                            }
                        } else if (zoomFactor > 50) {
                            // 中精度：每1024个单位一个刻度
                            for (let val = -32768; val <= 32767; val += 1024) {
                                values.push(val);
                            }
                        } else if (zoomFactor > 15) {
                             values = [-32768, -28672, -24576, -20480, -16384, -12288, -8192, -4096, 0, 4096, 8192, 12288, 16384, 20480, 24576, 28672, 32767];
                        } else if (zoomFactor > 10) {
                             values = [-32768, -24576, -16384, -8192, -4096, 0, 4096, 8192, 16384, 24576, 32767];
                         } else {
                             values = [-32768, -16384, -8192, 0, 8192, 16384, 32767];
                         }
                       break;
               }

               // 如果没有音频数据，使用默认刻度
               if (showDefaultTicks && values.length === 0) {
                   switch (this.amplitudeUnit) {
                       case 'db':
                           values = [-60, -48, -36, -24, -12, -6, 0, 6, 12, 18];
                           break;
                       case 'percent':
                           values = [-100, -75, -50, -25, 0, 25, 50, 75, 100];
                           break;
                       case 'linear':
                       default:
                           values = [-32768, -24576, -16384, -8192, 0, 8192, 16384, 24576, 32767];
                           break;
                   }
               }

               const amplitudeScale = (zoomFactor / 10) * 0.9;
               
               // 清除现有内容
               amplitudeAxis.innerHTML = '';
               
               // 添加网格线到波形容器
               
               if (waveformContainer) {
                   // 清除旧的网格线
                   waveformContainer.querySelectorAll('.amplitude-grid-line').forEach(line => line.remove());
               }
               
               // 调试信息：显示刻度数量和缩放级别
               this.log(`📊 纵轴刻度更新: 缩放${zoomFactor.toFixed(1)}x, 主刻度${values.length}个, 单位${this.amplitudeUnit}, 精度${precision}位, 高度${height}px`);

               // 检查纵轴元素状态
               if (!amplitudeAxis) {
                   this.log('❌ 纵轴元素未找到');
                   return;
               }

               this.log(`📊 纵轴元素: 宽度${amplitudeAxis.offsetWidth}px, 高度${amplitudeAxis.offsetHeight}px, 显示${amplitudeAxis.style.display || 'default'}`);

               // 计算刻度值
               this.calculateAmplitudeTicks(values, height, amplitudeScale, amplitudeAxis, waveformContainer, showDefaultTicks, precision);
           }
           
           calculateAmplitudeTicks(values, height, amplitudeScale, amplitudeAxis, waveformContainer, showDefaultTicks = false, precision = 0) {
               let ticksCreated = 0;
               let labelsCreated = 0;
               let gridsCreated = 0;

               // 获取当前缩放级别
               const zoomFactor = this.amplitudeZoom;

               // 为每个主要刻度值计算位置和显示
               values.forEach((value, index) => {
                   let normalizedValue = value;
                   if (this.amplitudeUnit === 'db') {
                       // dB范围从-60到+20，总共80dB范围，映射到-1到1
                       normalizedValue = (value + 60) / 80 * 2 - 1;
                   }
                   if (this.amplitudeUnit === 'percent') normalizedValue = value / 100;
                   if (this.amplitudeUnit === 'linear') normalizedValue = value / 32768;
                   
                   // 应用纵轴偏移
                   const y = height / 2 - ((normalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);
                   
                   if (y >= 0 && y <= height) {
                       // 主要刻度线
                       const majorTick = document.createElement('div');
                       majorTick.className = 'amplitude-tick major';
                       majorTick.style.position = 'absolute';
                       majorTick.style.top = (y - 1) + 'px';
                       majorTick.style.left = '0px';
                       majorTick.style.height = '2px';

                       // 主要刻度标签
                       const label = document.createElement('div');
                       label.className = 'amplitude-label';
                       label.style.position = 'absolute';
                       label.style.top = (y - 8) + 'px';  // 增加垂直偏移
                       label.style.left = '15px';
                       label.style.color = '#fff';
                       label.style.fontSize = '9px';      // 稍微增大字体
                       label.style.whiteSpace = 'nowrap';
                       label.style.fontFamily = 'monospace';
                       label.style.lineHeight = '1.2';    // 增加行高
                       label.style.padding = '1px 2px';   // 增加内边距
                       label.style.background = 'rgba(0,0,0,0.3)'; // 添加半透明背景提高可读性

                       // 网格线（添加到波形容器）- 只有在有音频数据时才添加
                       if (waveformContainer && !showDefaultTicks) {
                           const gridLine = document.createElement('div');
                           gridLine.className = 'amplitude-grid-line';
                           gridLine.style.position = 'absolute';
                           gridLine.style.top = y + 'px';
                           gridLine.style.left = '0px';
                           gridLine.style.right = '0px';
                           gridLine.style.height = '1px';
                           gridLine.style.zIndex = '1';

                           // 零线使用不同的样式
                           if (Math.abs(normalizedValue) < 0.01) {
                               gridLine.style.background = 'rgba(255, 255, 0, 0.3)';
                               gridLine.style.borderTop = '1px solid rgba(255, 255, 0, 0.5)';
                           } else {
                               gridLine.style.background = 'rgba(255, 255, 255, 0.15)';
                               gridLine.style.borderTop = '1px solid rgba(255, 255, 255, 0.1)';
                           }

                           waveformContainer.appendChild(gridLine);
                           gridsCreated++;
                       }
                       
                       // 添加次要刻度（在主要刻度之间）- 根据缩放级别动态调整
                       if (index < values.length - 1) {
                           let numMinorTicks;
                           // 减少高缩放时的副刻度密度，确保足够的视觉间距
                           if (zoomFactor > 1000) {
                               numMinorTicks = 1; // 极高缩放：仅1个副刻度，避免过密
                           } else if (zoomFactor > 500) {
                               numMinorTicks = 2; // 超高缩放：2个副刻度
                           } else if (zoomFactor > 200) {
                               numMinorTicks = 3; // 高缩放：3个副刻度
                           } else if (zoomFactor > 100) {
                               numMinorTicks = 4; // 中高缩放：4个副刻度
                           } else if (zoomFactor > 50) {
                               numMinorTicks = 4; // 中缩放：4个副刻度
                           } else if (zoomFactor > 25) {
                               numMinorTicks = 3; // 标准缩放：3个副刻度
                           } else if (values.length > 15) {
                               numMinorTicks = 1; // 刻度密集时：1个副刻度
                           } else if (values.length > 10) {
                               numMinorTicks = 2; // 中等密度：2个副刻度
                           } else {
                               numMinorTicks = 4; // 低密度：4个副刻度
                           }
                           this.addMinorTicks(value, values[index + 1], y, numMinorTicks, height, amplitudeScale, amplitudeAxis);
                       }
                       
                       let labelText;
                       switch (this.amplitudeUnit) {
                           case 'db':
                               labelText = value.toFixed(precision);
                               break;
                           case 'percent':
                               labelText = value.toFixed(precision) + '%';
                               break;
                           case 'linear':
                           default:
                                labelText = value.toString();
                               break;
                       }
                       
                       label.textContent = labelText;
                       amplitudeAxis.appendChild(majorTick);
                       amplitudeAxis.appendChild(label);

                       ticksCreated++;
                       labelsCreated++;
                   }
               });

               // 输出创建统计
               this.log(`📊 刻度创建完成: 主刻度${ticksCreated}个, 标签${labelsCreated}个, 网格线${gridsCreated}个`);
           }
           
           addMinorTicks(startValue, endValue, startY, numMinorTicks, height, amplitudeScale, amplitudeAxis) {
               const valueStep = (endValue - startValue) / (numMinorTicks + 1);

               // 计算下一个主刻度的Y位置
               let endNormalizedValue = endValue;
               if (this.amplitudeUnit === 'db') {
                   // dB范围从-60到+20，总共80dB范围，映射到-1到1
                   endNormalizedValue = (endValue + 60) / 80 * 2 - 1;
               }
               if (this.amplitudeUnit === 'percent') endNormalizedValue = endValue / 100;
               if (this.amplitudeUnit === 'linear') endNormalizedValue = endValue / 32768;

               const endY = height / 2 - ((endNormalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);
               const yStep = (endY - startY) / (numMinorTicks + 1);

               // 根据缩放级别调整副刻度的样式
               const zoomFactor = this.amplitudeZoom;
               let tickOpacity, tickWidth;
               if (zoomFactor > 200) {
                   tickOpacity = '0.8';
                   tickWidth = '15px'; // 高缩放时副刻度更明显
               } else if (zoomFactor > 100) {
                   tickOpacity = '0.7';
                   tickWidth = '12px';
               } else if (zoomFactor > 50) {
                   tickOpacity = '0.6';
                   tickWidth = '10px';
               } else {
                   tickOpacity = '0.5';
                   tickWidth = '8px';
               }

               for (let i = 1; i <= numMinorTicks; i++) {
                   const minorY = startY + (yStep * i);

                   if (minorY >= 0 && minorY <= height) {
                       const minorTick = document.createElement('div');
                       minorTick.className = 'amplitude-tick minor';
                       minorTick.style.position = 'absolute';
                       minorTick.style.top = (minorY - 0.5) + 'px';
                       minorTick.style.left = '0px';
                       minorTick.style.width = tickWidth;
                       minorTick.style.height = '1px';
                       minorTick.style.background = `rgba(255, 255, 255, ${tickOpacity})`;
                       minorTick.style.borderRight = '1px solid rgba(255, 255, 255, 0.3)';
                       amplitudeAxis.appendChild(minorTick);

                       // 在极高缩放时添加数值标签到部分副刻度
                       if (zoomFactor > 500 && i % 2 === 0) {
                           const minorValue = startValue + (valueStep * i);
                           const minorLabel = document.createElement('div');
                           minorLabel.className = 'amplitude-label minor';
                           minorLabel.style.position = 'absolute';
                           minorLabel.style.top = (minorY - 6) + 'px';
                           minorLabel.style.left = (tickWidth === '15px' ? '18px' : '15px');
                           minorLabel.style.fontSize = '9px';
                           minorLabel.style.color = 'rgba(255, 255, 255, 0.7)';
                           minorLabel.style.whiteSpace = 'nowrap';

                           let labelText;
                           switch (this.amplitudeUnit) {
                               case 'db':
                                   labelText = minorValue.toFixed(2);
                                   break;
                               case 'percent':
                                   labelText = minorValue.toFixed(1) + '%';
                                   break;
                               case 'linear':
                               default:
                                   labelText = Math.round(minorValue).toString();
                                   break;
                           }
                           minorLabel.textContent = labelText;
                           amplitudeAxis.appendChild(minorLabel);
                       }
                   }
               }
           }
           // 设置右键菜单
           setupContextMenu() {
               this.contextMenuOverlay = document.getElementById('contextMenuOverlay');
               this.contextMenu = document.getElementById('contextMenu');
               const waveformContainer = document.querySelector('.waveform-container');
               
               // 存储鼠标按下位置
               this.contextMenuPosition = { x: 0, y: 0 };
                          
               // 右键事件
               waveformContainer.addEventListener('mousedown', (e) => {
                   if (e.button === 2) { // 右键
                       // 记录鼠标按下位置并保存选择状态
                       this.contextMenuPosition = { x: e.clientX, y: e.clientY };
                       this.saveSelectionState();
                   }
               });
       
  
               waveformContainer.addEventListener('contextmenu', (e) => {
                   //e.preventDefault();
                   // 使用按下位置而非释放位置
                   //this.showContextMenu(this.contextMenuPosition.x, this.contextMenuPosition.y);


                   e.preventDefault();
                
                   // 恢复选择状态（保持框选区域显示）
                   this.restoreSelectionState();
  
                    // 直接使用鼠标坐标
                    const mouseX = this.contextMenuPosition ? this.contextMenuPosition.x : e.clientX;
                    const mouseY = this.contextMenuPosition ? this.contextMenuPosition.y : e.clientY;
                    
                    // 强制设置位置
                    contextMenu.style.position = 'fixed';
                    contextMenu.style.left = this.contextMenuPosition.x + 'px';
                    contextMenu.style.top = this.contextMenuPosition.y + 'px';
                    contextMenu.style.display = 'block';
                    //contextMenu.style.backgroundColor = '#ffffff';
                    contextMenu.style.background = '#ffffff';
                    //contextMenu.style.borderRadius = '4px';
                    contextMenu.style.boxShadow = '0 4px 15px rgba(0,0,0,0.5)';
                    contextMenu.style.zIndex = '99999';
                    contextMenu.style.opacity = '1';
                    contextMenu.style.backdropFilter = 'none';
                    contextMenu.style.webkitBackdropFilter = 'none';

                    const menu = this.contextMenu;
                    const items = menu.querySelectorAll('.context-menu-item');
                    items.forEach(item => {
                        item.style.backgroundColor = '#ffffff';
                        item.style.background = '#ffffff';
                        
                        item.style.padding = '10px 16px';
                        item.style.borderBottom = '1px solid #dddddd';
                        
                        // 悬停事件
                        item.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#007acc';
                            this.style.background = '#007acc';
                            this.style.color = '#00';
                        });
                        
                        item.addEventListener('mouseleave', function() {
                            if (!this.classList.contains('disabled')) {
                                this.style.backgroundColor = '#ffffff';
                                this.style.background = '#ffffff';
                                this.style.color = '#333333';
                            }
                        });
                    });
                    console.log('菜单位置:', this.contextMenuPosition.x, this.contextMenuPosition.y);
               });
               
               // 菜单项点击事件
               document.querySelectorAll('.context-menu-item').forEach(item => {
                   item.addEventListener('click', (e) => {
                       e.stopPropagation();
                       if (!item.classList.contains('disabled') && !item.classList.contains('has-submenu')) {
                           const action = item.dataset.action;
                           this.executeContextAction(action);
                           this.hideContextMenu();
                       }
                   });
               });
               
               // 子菜单显示/隐藏
               const volumeItem = document.querySelector('[data-action="volume"]');
               const submenu = volumeItem.querySelector('.context-submenu');
               let submenuTimer = null;

               // 初始化子菜单箭头
               this.initializeSubmenuArrows();
               volumeItem.addEventListener('mouseenter', () => {
                    clearTimeout(submenuTimer);
                    this.showSubmenu(volumeItem, submenu);
               });
               
               volumeItem.addEventListener('mouseleave', () => {

                   submenuTimer = setTimeout( () => {
                       if (!submenu.matches(':hover') && !volumeItem.matches(':hover')) 
                       {
                           this.hideSubmenu(submenu);
                       }
                   }, 150);
               });
               
               submenu.addEventListener('mouseleave', () => {
                   submenuTimer = setTimeout(() => {
                        if (!volumeItem.matches(':hover')) {
                           this.hideSubmenu(submenu);
                       }
                   }, 100);
               });
               
               submenu.addEventListener('mouseenter', () => {
                   clearTimeout(submenuTimer);
               });
               
               // 背景遮罩点击事件 - 只响应左键
               this.contextMenuOverlay.addEventListener('mousedown', (e) => {
                   if (e.button === 0) { // 左键
                       e.preventDefault();
                       e.stopPropagation();
                       this.hideContextMenu();
                   }
               });
               
               // 全局左键点击隐藏菜单（备用）
               document.addEventListener('mousedown', (e) => {
                   if (e.button === 0 && this.contextMenu.style.display === 'block') {
                       if (!this.contextMenu.contains(e.target)) {
                           this.hideContextMenu();
                       }
                   }
               });
               
               // 更新贴上状态
               this.updatePasteStatus();
           }
                     
           // 初始化子菜单箭头
           initializeSubmenuArrows() {
               const submenuItems = document.querySelectorAll('.context-menu-item.has-submenu');
               submenuItems.forEach(item => {
                   // 确保箭头存在
                   if (!item.querySelector('.submenu-arrow')) {
                       const arrow = document.createElement('span');
                       arrow.className = 'submenu-arrow';
                       arrow.textContent = '▶';
                       arrow.style.float = 'right';
                       arrow.style.marginLeft = '20px';
                       arrow.style.color = '#666666';
                       item.appendChild(arrow);
                   }
               });
           }
           
           // 显示子菜单
           showSubmenu(parentItem, submenu) {
               // 强制设置子菜单样式
               submenu.style.position = 'absolute';
               submenu.style.display = 'block';
               submenu.style.backgroundColor = '#ffffff';
               submenu.style.background = '#ffffff';
               submenu.style.border = '2px solid #333333';
               submenu.style.borderRadius = '4px';
               submenu.style.boxShadow = '0 4px 15px rgba(0,0,0,0.5)';
               submenu.style.zIndex = '99998';
               submenu.style.minWidth = '140px';
               
               // 设置子菜单项样式
               const submenuItems = submenu.querySelectorAll('.context-menu-item');
               submenuItems.forEach(item => {
                   item.style.backgroundColor = '#ffffff';
                   item.style.background = '#ffffff';
                   item.style.color = '#333333';
                   item.style.padding = '8px 16px';
                   item.style.borderBottom = '1px solid #dddddd';
                   
                   // 移除已有的事件监听器，避免重复绑定
                   item.removeEventListener('mouseenter', item._mouseenterHandler);
                   item.removeEventListener('mouseleave', item._mouseleaveHandler);
                   
                   // 悬停事件
                   item._mouseenterHandler = function() {
                       this.style.backgroundColor = '#007acc';
                       this.style.background = '#007acc';
                       this.style.color = '#ffffff';
                   };
                   
                   item._mouseleaveHandler = function() {
                       if (!this.classList.contains('disabled')) {
                           this.style.backgroundColor = '#ffffff';
                           this.style.background = '#ffffff';
                           this.style.color = '#333333';
                       }
                   };
                   
                   item.addEventListener('mouseenter', item._mouseenterHandler);
                   item.addEventListener('mouseleave', item._mouseleaveHandler);
               });
               
               // 计算位置 - 显示在右侧
               const parentRect = parentItem.getBoundingClientRect();
               const submenuWidth = 140;
               const submenuHeight = submenu.offsetHeight || 200;
               
               let left = parentRect.right;
               let top = parentRect.top;
               
               // 检查右边界
               if (left + submenuWidth > window.innerWidth) {
                   left = parentRect.left - submenuWidth; // 显示在左侧
               }
               
               // 检查下边界
               if (top + submenuHeight > window.innerHeight) {
                   top = window.innerHeight - submenuHeight - 10;
               }
               
               // 检查上边界
               if (top < 0) {
                   top = 10;
               }
               
               submenu.style.left = left + 'px';
               submenu.style.top = top + 'px';
               
               console.log(`子菜单显示在: (${left}, ${top})`);
           }
           
           // 隐藏子菜单
           hideSubmenu(submenu) {
               submenu.style.display = 'none';
           }

           showContextMenu(x, y) {
               if (!this.audioData) return;
               
               // 确保选择区域保持显示
               this.restoreSelectionState();
               this.updatePasteStatus();
               
               // 显示背景遮罩
               this.contextMenuOverlay.style.display = 'block';

               // 设置菜单位置
               this.contextMenu.style.left = x + 'px';
               this.contextMenu.style.top = y + 'px';
               this.contextMenu.style.display = 'block';
               // 强制重绘以确保动画效果
               this.contextMenu.offsetHeight;
               
               // 添加显示类用于动画
               setTimeout(() => {
                   this.contextMenu.classList.add('show');
               }, 10);
     
               // 确保菜单不超出屏幕
               const rect = this.contextMenu.getBoundingClientRect();
               if (rect.right > window.innerWidth) {
                   this.contextMenu.style.left = (x - rect.width) + 'px';
               }
               if (rect.bottom > window.innerHeight) {
                   this.contextMenu.style.top = (y - rect.height) + 'px';
               }
            
               // 如果调整后仍超出上边界，则向下显示
               const adjustedRect = this.contextMenu.getBoundingClientRect();
               if (adjustedRect.top < 0) {
                   this.contextMenu.style.top = '10px';
               }
               
               this.log('📋 右键菜单已显示');
           }
           
           // 隐藏所有子菜单
           hideContextMenu() {
                // 隐藏背景遮罩
               this.contextMenuOverlay.style.display = 'none';
               
               // 移除显示类
               this.contextMenu.classList.remove('show');
               
               // 延迟隐藏菜单以允许动画完成
               setTimeout(() => {
               this.contextMenu.style.display = 'none';
                }, 100);
               document.querySelectorAll('.context-submenu').forEach(submenu => {
                  
                   this.hideSubmenu(submenu);
               });
               // 注意：不清除选择状态，保持框选区域显示
               // this.selectionStart = this.selectionEnd = 0; // 移除这行
               // document.getElementById('selectionBox').style.display = 'none'; // 移除这行
           }
           
           updatePasteStatus() {
               const pasteItem = document.getElementById('pasteItem');
               if (this.clipboard && this.clipboard.length > 0) {
                   pasteItem.classList.remove('disabled');
                   pasteItem.textContent = `贴上 (${this.formatTime(this.clipboard.length / this.sampleRate)})`;
               } else {
                   pasteItem.classList.add('disabled');
                   pasteItem.textContent = '贴上';
               }
           }
           
           executeContextAction(action) {
               this.log(`🔧 执行操作: ${action}`);
               switch(action) {
                   case 'cut':
                       this.cutSelection();
                       break;
                   case 'copy':
                       this.copySelection();
                       break;
                   case 'paste':
                       this.pasteFromClipboard();
                       break;
                   case 'amplify-150':
                       this.amplifySelection(1.5);
                       break;
                   case 'amplify-200':
                       this.amplifySelection(2.0);
                       break;
                   case 'reduce-50':
                       this.amplifySelection(0.5);
                       break;
                   case 'reduce-25':
                       this.amplifySelection(0.25);
                       break;
                   case 'normalize':
                       this.normalizeSelection();
                       break;
               }
           }
           
           cutSelection() {
               if (this.selectionStart >= this.selectionEnd) {
                   this.log('❌ 请先选择一个区域');
                   return;
               }
               
               this.copySelection();
               
               const startSample = Math.floor(this.selectionStart * this.sampleRate);
               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
               
               // 创建新的音频数组，移除选中部分
               const newAudioData = new Float32Array(this.audioData.length - (endSample - startSample));
               let writeIndex = 0;
               
               // 复制选择前的部分
               for (let i = 0; i < startSample; i++) {
                   newAudioData[writeIndex++] = this.audioData[i];
               }
               
               // 复制选择后的部分
               for (let i = endSample; i < this.audioData.length; i++) {
                   newAudioData[writeIndex++] = this.audioData[i];
               }
               
               this.audioData = newAudioData;
               this.totalDuration = this.audioData.length / this.sampleRate;
               
               // 清除选择
               this.selectionStart = this.selectionEnd = 0;
               document.getElementById('selectionBox').style.display = 'none';
               // 清除保存的选择状态，防止缩放时重新出现
               this.lastSelectionState = null;
               
               this.updateAudioBuffer();
               this.autoFitWaveform();
               
               this.log(`✂️ 剪裁完成，新时长: ${this.formatTime(this.totalDuration)}`);
           }
           
           copySelection() {
               if (this.selectionStart >= this.selectionEnd) {
                   this.log('❌ 请先选择一个区域');
                   return;
               }
               
               const startSample = Math.floor(this.selectionStart * this.sampleRate);
               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
               
               this.clipboard = new Float32Array(endSample - startSample);
               for (let i = 0; i < this.clipboard.length; i++) {
                   this.clipboard[i] = this.audioData[startSample + i];
               }
               
               const duration = this.clipboard.length / this.sampleRate;
               this.log(`📋 已复制 ${this.formatTime(duration)} 到暂存区`);
           }
           
           pasteFromClipboard() {
               if (!this.clipboard || this.clipboard.length === 0) {
                   this.log('❌ 暂存区为空');
                   return;
               }
               
               // 简单的粘贴到选择位置
               const insertPosition = this.selectionStart > 0 ? 
                   Math.floor(this.selectionStart * this.sampleRate) : 0;
               
               const newAudioData = new Float32Array(this.audioData.length + this.clipboard.length);
               let writeIndex = 0;
               
               // 复制插入位置前的数据
               for (let i = 0; i < insertPosition; i++) {
                   newAudioData[writeIndex++] = this.audioData[i];
               }
               
               // 插入剪贴板数据
               for (let i = 0; i < this.clipboard.length; i++) {
                   newAudioData[writeIndex++] = this.clipboard[i];
               }
               
               // 复制插入位置后的数据
               for (let i = insertPosition; i < this.audioData.length; i++) {
                   newAudioData[writeIndex++] = this.audioData[i];
               }
               
               this.audioData = newAudioData;
               this.totalDuration = this.audioData.length / this.sampleRate;
               
               this.updateAudioBuffer();
               this.autoFitWaveform();
               
               const duration = this.clipboard.length / this.sampleRate;
               this.log(`📌 已粘贴 ${this.formatTime(duration)}，新时长: ${this.formatTime(this.totalDuration)}`);
           }
           
           amplifySelection(factor) {
               if (this.selectionStart >= this.selectionEnd) {
                   this.log('❌ 请先选择一个区域');
                   return;
               }
               
               const startSample = Math.floor(this.selectionStart * this.sampleRate);
               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
               
               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
                   this.audioData[i] = Math.max(-1, Math.min(1, this.audioData[i] * factor));
               }
               
               this.updateAudioBuffer();
               
               const percentage = Math.round(factor * 100);
               this.log(`🔊 选择区域音量调整为 ${percentage}%`);
           }
           
           normalizeSelection() {
               if (this.selectionStart >= this.selectionEnd) {
                   this.log('❌ 请先选择一个区域');
                   return;
               }
               
               const startSample = Math.floor(this.selectionStart * this.sampleRate);
               const endSample = Math.floor(this.selectionEnd * this.sampleRate);
               
               // 找到最大振幅
               let maxAmplitude = 0;
               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
                   maxAmplitude = Math.max(maxAmplitude, Math.abs(this.audioData[i]));
               }
               
               if (maxAmplitude === 0) {
                   this.log('⚠️ 选择区域无信号');
                   return;
               }
               
               // 计算标准化因子（留出一点余量）
               const normalizeFactor = 0.95 / maxAmplitude;
               
               for (let i = startSample; i < endSample && i < this.audioData.length; i++) {
                   this.audioData[i] *= normalizeFactor;
               }
               
               this.updateAudioBuffer();
               
               this.log(`📏 选择区域已标准化，增益: ${(20 * Math.log10(normalizeFactor)).toFixed(1)}dB`);
           }
           
           updateAudioBuffer() {
               if (this.audioContext && this.audioData) {
                   try {
                       this.audioBuffer = this.audioContext.createBuffer(1, this.audioData.length, this.sampleRate);
                       const channelData = this.audioBuffer.getChannelData(0);
                       for (let i = 0; i < this.audioData.length; i++) {
                           channelData[i] = this.audioData[i];
                       }
                       // 重新初始化播放控制
                       this.initializePlaybackControls();
                       // 播放控制将在processFile最后统一启用
                   } catch (error) {
                       this.log('⚠️ 音频缓冲区更新失败: ' + error.message);
                   }
               }
           }
            updateLayout() {
                // 同步更新時間軸和縱軸
               this.updateTimeline();
                this.updateAmplitudeAxis();
                // 更新播放頭位置
                this.updatePlayhead();
                // 確保canvas尺寸正確
                this.setupCanvases();
            }
            async processFile(file) {
                try {
                    this.log(`🔄 開始處理文件: ${file.name}`);
                    // 如果已经有文件加载，显示重新加载信息
                    if (this.audioData) {
                        this.log('🔄 重新載入新文件，清除舊數據...');
                        this.clearPreviousData();
                    }
                   
                    // 檢查文件類型
                    const isWav = file.type.includes('wav') || file.name.toLowerCase().endsWith('.wav');
                    if (!isWav) {
                        throw new Error('不是WAV文件格式');
                    }
                    
                    // 讀取文件
                    const arrayBuffer = await file.arrayBuffer();
                    this.log(`文件讀取完成，大小: ${arrayBuffer.byteLength} 字節`);
                    
                    // 初始化音頻上下文
                    try {
                        await this.initAudioContext();
                    } catch (error) {
                        this.log('⚠️ 音頻播放功能不可用，但可以查看波形');
                    }
                    // 解析WAV文件
                    const audioData = this.parseWav(arrayBuffer);
                    this.log(`WAV解析成功，採樣率: ${audioData.sampleRate}Hz, 樣本數: ${audioData.samples.length}`);
                    
                    // 保存數據
                    this.audioData = audioData.samples;
                    this.sampleRate = audioData.sampleRate;
                    this.totalDuration = audioData.samples.length / audioData.sampleRate;                   

                    
                    // 創建AudioBuffer用於播放
                    if (this.audioContext) {
                        try {
                            this.audioBuffer = this.audioContext.createBuffer(1, audioData.samples.length, audioData.sampleRate);
                            const channelData = this.audioBuffer.getChannelData(0);
                            for (let i = 0; i < audioData.samples.length; i++) {
                                channelData[i] = audioData.samples[i];
                            }
                            this.enablePlaybackControls();
                        } catch (error) {
                            this.log('⚠️ 音頻緩衝區創建失敗: ' + error.message);
                        }
                    }
                    // 更新顯示緩衝區
                    const displayLength = Math.min(8192, this.audioData.length);
                    this.displayBuffer = new Float32Array(displayLength);
                    for (let i = 0; i < displayLength; i++) {
                        this.displayBuffer[i] = this.audioData[i];
                    }
                    
                    // 計算頻譜
                    this.computeSpectrum();

                    // 如果當前是頻譜圖模式，生成頻譜圖數據
                    if (this.displayMode === 'spectrogram') {
                        this.generateSpectrogram();
                    }
                    
                    // 顯示文件信息
                    this.showFileInfo(file, audioData);
                    
                   // 隱藏拖拽區域，显示波形区域
                    const dropZone = document.getElementById('dropZone');
                    dropZone.classList.add('hidden');


                   
                    // 在最后重置选择和启用播放（确保AudioBuffer已创建）
                    this.resetSelection();
                    this.resetPlaybackStateButKeepEnabled();
                    // 明确启用播放控制
                    this.enablePlaybackControls();
                    // 強制重新調整canvas尺寸
                    this.updateLayout();
                    // 自動調整縮放
                    this.autoFitWaveform();

                    // 设置滑块限制（基于动态最小缩放级别）
                    this.setupZoomSliderLimits();

                    this.log('✅ 文件處理完成，可以開始分析');
                    
                } catch (error) {
                    this.log(`❌ 處理文件時出錯: ${error.message}`);
                    console.error('處理文件錯誤:', error);
                }
            }
            
            // 清除之前的数据
            clearPreviousData() {
                // 停止当前播放
                if (this.isPlaying) {
                    this.stopAudio();
                }
                
                // 清除音频数据
                this.audioData = null;
                this.audioBuffer = null;
                this.displayBuffer = null;
                this.spectrumData = new Uint8Array(1024);
                
                // 重置音频参数
                this.totalDuration = 0;
                this.currentPlayTime = 0;
                this.pauseTime = 0;
                this.lastPlayPosition = 0;

                // 清除剪贴板
                this.clipboard = null;
                this.updatePasteStatus();

                // 禁用播放控制（清除数据时）
                this.disablePlaybackControls();
            }
            // 重置播放状态但保持按钮启用（用于加载新文件）
            resetPlaybackStateButKeepEnabled() {
                // 先停止任何正在播放的音频
                if (this.isPlaying && this.audioSource) {
                    this.audioSource.stop();
                    this.audioSource = null;
                }
                
                this.isPlaying = false;
                this.currentPlayTime = 0;
                this.pauseTime = 0;
                this.startTime = 0;
                this.lastPlayPosition = 0; // 重置最后播放位置

                // 重置时间显示和播放头
                document.getElementById('timeDisplay').textContent = '0:00.000';
                this.updatePlayhead();
                
                // 重置按钮文本但不禁用
                document.getElementById('playBtn').textContent = '播放';
            }
            
            // 原有的重置方法（用于清除数据时
            resetPlaybackState() {
                // 先停止任何正在播放的音频
                if (this.isPlaying && this.audioSource) {
                    this.audioSource.stop();
                    this.audioSource = null;
                }
                this.isPlaying = false;
                this.currentPlayTime = 0;
                this.pauseTime = 0;
                this.startTime = 0;
                
                // 重置时间显示但不禁用按钮（如果有音频数据）
                document.getElementById('timeDisplay').textContent = '0:00.000';
                this.updatePlayhead(); // 重置播放头位置
            }
            
            // 重置播放状态（用于清除数据时）
            resetSelection() {
                this.selectionStart = 0;
                this.selectionEnd = 0;

                // 隐藏选择框
                const selectionBox = document.getElementById('selectionBox');
                selectionBox.style.display = 'none';

                // 清除保存的选择状态，防止缩放时重新出现
                this.lastSelectionState = null;

                // 重置选择信息显示
                document.getElementById('selBegin').textContent = '0:00.000';
                document.getElementById('selEnd').textContent = '0:00.000';
                document.getElementById('selLength').textContent = '0:00.000';
            }
            // 修复播放控制初始化
            initializePlaybackControls() {
                const playBtn = document.getElementById('playBtn');
                const stopBtn = document.getElementById('stopBtn');
                
                // 移除现有事件监听器
                playBtn.replaceWith(playBtn.cloneNode(true));
                stopBtn.replaceWith(stopBtn.cloneNode(true));
                
                // 重新添加事件监听器
                document.getElementById('playBtn').addEventListener('click', () => this.playAudio());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopAudio());
                
                this.log('🔧 播放控制已重新初始化');
            }
            parseWav(arrayBuffer) {
                const view = new DataView(arrayBuffer);
                
                // 檢查RIFF頭
                const riff = this.readString(view, 0, 4);
                if (riff !== 'RIFF') {
                    throw new Error('不是有效的RIFF文件');
                }
                
                const wave = this.readString(view, 8, 4);
                if (wave !== 'WAVE') {
                    throw new Error('不是WAV文件');
                }
                
                // 查找fmt和data塊
                let offset = 12;
                let fmtChunk = null;
                let dataChunk = null;
                
                while (offset < arrayBuffer.byteLength - 8) {
                    const chunkId = this.readString(view, offset, 4);
                    const chunkSize = view.getUint32(offset + 4, true);
                    
                    if (chunkId === 'fmt ') {
                        fmtChunk = this.parseFmtChunk(view, offset + 8);
                    } else if (chunkId === 'data') {
                        dataChunk = { offset: offset + 8, size: chunkSize };
                        break;
                    }
                    
                    offset += 8 + chunkSize;
                    if (chunkSize % 2 === 1) offset++; // 對齊
                }
                
                if (!fmtChunk || !dataChunk) {
                    throw new Error('WAV文件格式不完整');
                }
                
                // 解析音頻數據
                const samples = this.parseAudioData(view, dataChunk, fmtChunk);
                
                return {
                    sampleRate: fmtChunk.sampleRate,
                    channels: fmtChunk.channels,
                    bitsPerSample: fmtChunk.bitsPerSample,
                    samples: samples
                };
            }
            
            readString(view, offset, length) {
                let result = '';
                for (let i = 0; i < length; i++) {
                    result += String.fromCharCode(view.getUint8(offset + i));
                }
                return result;
            }
            
            parseFmtChunk(view, offset) {
                return {
                    audioFormat: view.getUint16(offset, true),
                    channels: view.getUint16(offset + 2, true),
                    sampleRate: view.getUint32(offset + 4, true),
                    byteRate: view.getUint32(offset + 8, true),
                    blockAlign: view.getUint16(offset + 12, true),
                    bitsPerSample: view.getUint16(offset + 14, true)
                };
            }
            
            parseAudioData(view, dataChunk, fmtChunk) {
                const bytesPerSample = fmtChunk.bitsPerSample / 8;
                const numSamples = dataChunk.size / (bytesPerSample * fmtChunk.channels);
                const samples = new Float32Array(numSamples);
                
                let dataOffset = dataChunk.offset;
                
                for (let i = 0; i < numSamples; i++) {
                    let sample = 0;
                    
                    if (fmtChunk.bitsPerSample === 16) {
                        sample = view.getInt16(dataOffset, true) / 32768.0;
                    } else if (fmtChunk.bitsPerSample === 8) {
                        sample = (view.getUint8(dataOffset) - 128) / 128.0;
                    } else if (fmtChunk.bitsPerSample === 24) {
                        const byte1 = view.getUint8(dataOffset);
                        const byte2 = view.getUint8(dataOffset + 1);
                        const byte3 = view.getUint8(dataOffset + 2);
                        sample = ((byte3 << 16) | (byte2 << 8) | byte1);
                        if (sample >= 0x800000) sample -= 0x1000000;
                        sample = sample / 8388608.0;
                    }
                    
                    samples[i] = sample;
                    dataOffset += bytesPerSample * fmtChunk.channels; // 只取第一聲道
                }
                
                return samples;
            }
            
            computeSpectrum() {
                if (!this.displayBuffer || this.displayBuffer.length === 0) return;
                
                // 簡單的頻譜計算
                for (let i = 0; i < this.spectrumData.length; i++) {
                    const freq = i / this.spectrumData.length;
                    let amplitude = 0;
                    
                    // 計算該頻率的能量
                    for (let j = 0; j < Math.min(this.displayBuffer.length, 512); j++) {
                        const phase = 2 * Math.PI * freq * j / 1024;
                        amplitude += this.displayBuffer[j] * Math.cos(phase);
                    }
                    
                    amplitude = Math.abs(amplitude) / this.displayBuffer.length;
                    this.spectrumData[i] = Math.min(255, amplitude * 1000);
                }
            }
            
            showFileInfo(file, audioData) {
                const fileInfo = document.getElementById('fileInfo');
                const duration = audioData.samples.length / audioData.sampleRate;
                
                fileInfo.innerHTML = `
                    <div><strong>📄 ${file.name}</strong></div>
                    <div>大小: ${(file.size / 1024).toFixed(1)} KB</div>
                    <div>採樣率: ${audioData.sampleRate} Hz</div>
                    <div>聲道: ${audioData.channels}</div>
                    <div>位深: ${audioData.bitsPerSample} 位</div>
                    <div>時長: ${duration.toFixed(2)} 秒</div>
                    <div>樣本: ${audioData.samples.length.toLocaleString()}</div>
                `;
                
                fileInfo.style.display = 'block';
                
                // 更新統計面板
                document.getElementById('sampleRateDisplay').textContent = audioData.sampleRate + ' Hz';
                document.getElementById('durationDisplay').textContent = duration.toFixed(2) + ' s';
                document.getElementById('channelsDisplay').textContent = audioData.channels;
                document.getElementById('quality').textContent = '優秀';
            }
            
            updateViewOffset() {
                if (!this.totalDuration) return;
                
                // 計算基於當前縮放的最大偏移量
                const totalWidth = this.totalDuration * this.pixelsPerSecond;
                this.maxViewOffset = Math.max(0, totalWidth - this.canvas.clientWidth);
                
                // 限制視角偏移範圍
                this.viewOffset = Math.max(0, Math.min(this.maxViewOffset, this.viewOffset));
            }
             // 新增方法：確保視角不超出音頻實際範圍
             clampViewToAudioBounds() {
                if (!this.totalDuration || !this.canvas) return;
                
               const containerWidth = this.canvas.clientWidth;
               const totalAudioWidth = this.totalDuration * this.pixelsPerSecond;
                
               // 检查是否处于最小缩放级别
               const isAtMinZoom = this.hasAutoFitted && this.currentZoomIndex <= this.autoFitZoomIndex;
 

              // 如果音频总宽度小于容器宽度或处于最小缩放，居中显示
               if (totalAudioWidth <= containerWidth || isAtMinZoom) {
                   this.viewOffset = -(containerWidth - totalAudioWidth) / 2;
                   console.log('音频完全可见，居中显示');
                    if (isAtMinZoom) {
                       this.viewOffset = 0; // 最小缩放时重置为0
                   }
                   return;
               }
               


               // 计算有效的偏移范围
               const maxOffset = Math.max(0, totalAudioWidth - containerWidth);
               const minOffset = 0;
                

               // 限制视角偏移范围
               const oldOffset = this.viewOffset;
               this.viewOffset = Math.max(minOffset, Math.min(maxOffset, this.viewOffset));
 
            }
            // 獲取當前視圖的時間範圍
            getVisibleTimeRange() {
                 // 确保必要的变量有效
                if (!this.canvas || isNaN(this.pixelsPerSecond) || this.pixelsPerSecond <= 0) {
                    console.warn('Invalid state in getVisibleTimeRange:', {
                        canvas: !!this.canvas, pixelsPerSecond: this.pixelsPerSecond, viewOffset: this.viewOffset
                    });
                    return { startTime: 0, endTime: 0 };
                }
                const startTime = this.viewOffset / this.pixelsPerSecond;
                const endTime = (this.viewOffset + this.canvas.clientWidth) / this.pixelsPerSecond;

                
               // 確保不超出音頻範圍
                const clampedEndTime = Math.min(endTime, this.totalDuration);
                return { startTime, endTime: clampedEndTime };
            }
            
            autoFitWaveform() {
                if (this.totalDuration > 0) {
                    const containerWidth = this.canvas.clientWidth;

                    // 确保containerWidth有效
                    if (!containerWidth || containerWidth <= 0) {
                        console.warn('Invalid containerWidth:', containerWidth);
                        return;
                    }
                    // 計算能顯示整個音頻的縮放級別
                    const requiredPixelsPerSecond = containerWidth / this.totalDuration;

                    const autoFitZoom = Math.max(0.1, requiredPixelsPerSecond / 10);

                    // 计算动态最小缩放级别
                    const dynamicMinIndex = this.calculateDynamicMinZoom();
                    const dynamicMinZoomLevel = this.zoomSteps[dynamicMinIndex];

                    // 使用动态计算的最小缩放级别，而不是固定的默认级别
                    const finalAutoFitZoom = Math.max(autoFitZoom, dynamicMinZoomLevel);

                    this.log(`🔍 自动适配详细计算:`);
                    this.log(`   音频时长: ${this.totalDuration.toFixed(2)}s`);
                    this.log(`   容器宽度: ${containerWidth}px`);
                    this.log(`   需求像素/秒: ${requiredPixelsPerSecond.toFixed(2)}`);
                    this.log(`   计算缩放: ${autoFitZoom.toFixed(3)}`);
                    this.log(`   动态最小: ${dynamicMinZoomLevel} (索引${dynamicMinIndex})`);
                    this.log(`   最终缩放: ${finalAutoFitZoom.toFixed(3)}`);

                    // 记录自动适配的缩放级别
                    this.autoFitZoomIndex = Math.max(dynamicMinIndex, this.zoomSteps.findIndex(zoom => zoom >= finalAutoFitZoom));

                    if (this.autoFitZoomIndex === -1) {
                        this.autoFitZoomIndex = dynamicMinIndex;
                    }
                    this.hasAutoFitted = true;

                    // 使用固定阶数缩放
                    this.setZoomLevel(finalAutoFitZoom);

                    // 更新滑块范围，使用动态最小值
                    const slider = document.getElementById('zoomSlider');

                    if (slider) {
                        slider.min = this.zoomSteps[dynamicMinIndex]; // 使用动态最小值
                        slider.max = this.zoomSteps[this.zoomSteps.length - 1];
                        slider.value = this.zoomLevel;
                    }
                     // 重置視角偏移
                     this.viewOffset = 0;
                     this.amplitudeOffset = 0; // 重置纵轴偏移
                    this.updateZoom();
                    this.updateAmplitudeAxis();
                    this.log(`🔍 自动缩放: ${this.zoomLevel.toFixed(2)}倍，级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length}，最小级别: ${this.autoFitZoomIndex + 1}，预设最小: ${this.defaultLoadZoomIndex + 1}`);                    
                }
            }
         
            // 重置缩放限制到载入时预设值
            resetZoomToDefault() {
                if (this.currentZoomIndex !== this.defaultLoadZoomIndex) {
                    this.currentZoomIndex = this.defaultLoadZoomIndex;
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                    document.getElementById('zoomSlider').value = this.zoomLevel;
                    this.updateZoom();
                    this.log(`🔄 重置到载入预设缩放级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length}`);
                }
            }
            
            // 检查当前缩放是否在允许范围内
            isZoomLevelValid() {
                const dynamicMinIndex = this.calculateDynamicMinZoom();
                return this.currentZoomIndex >= dynamicMinIndex;
            }

            // 强制修正无效的缩放级别
            enforceZoomLimits() {
                if (!this.isZoomLevelValid()) {
                    const dynamicMinIndex = this.calculateDynamicMinZoom();
                    this.log(`⚠️ 检测到无效缩放级别 ${this.currentZoomIndex + 1}，修正为动态最小值 ${dynamicMinIndex + 1}`);
                    this.currentZoomIndex = dynamicMinIndex;
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                    document.getElementById('zoomSlider').value = this.zoomLevel;
                    this.updateZoom();
                }
            }
 
            enablePlaybackControls() {
                document.getElementById('playBtn').disabled = false;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('playBtn').textContent = '播放';
                this.updatePlaybackButtonStates();
                this.log('🎵 播放控制已啟用');
            }
            
            disablePlaybackControls() {
                document.getElementById('playBtn').disabled = true;
                document.getElementById('stopBtn').disabled = true;
                this.updatePlaybackButtonStates();
            }
            
            async playAudio() {
                this.log('🎵 尝试播放音频...');
                if (!this.audioBuffer || !this.audioContext) {
                    this.log('❌ 沒有可播放的音頻');
                    return;
                }
              
                // 基本数据有效性检查
                if (!this.audioData || this.audioData.length === 0) {
                    this.log('❌ 音频数据无效，无法播放');
                    return;
                }

                // 检查总时长有效性
                if (!this.totalDuration || this.totalDuration <= 0) {
                    this.log('❌ 音频时长无效，无法播放');
                    return;
                }

                // 确保pixelsPerSecond有效
                if (isNaN(this.pixelsPerSecond) || this.pixelsPerSecond <= 0) {
                    this.updatePixelsPerSecond();
                    this.log('🔧 修复了无效的pixelsPerSecond值');
                }

                // 检查并修复viewOffset
                if (isNaN(this.viewOffset)) {
                    this.viewOffset = 0;
                    this.log('🔧 修复了无效的viewOffset值');
                }
                try {
                    // 如果正在播放，先停止
                    if (this.isPlaying) {
                        this.stopAudio();
                        this.log('⏸️ 停止当前播放');
                        return;
                    }
                    // 確保音頻上下文處於運行狀態
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                        this.log('🔊 音频上下文已恢复');
                    }
                    


                    // 創建音頻源
                    this.audioSource = this.audioContext.createBufferSource();
                    this.audioSource.buffer = this.audioBuffer;
                    this.audioSource.connect(this.audioContext.destination);

                    // 設置播放結束回調
                    this.audioSource.onended = () => {
                        this.isPlaying = false;
 
                        // 播放结束时设置正确的位置
                        if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
                            // 如果是选择区域播放，结束位置是选择结束位置
                            this.lastPlayPosition = this.selectionEnd;
                            this.currentPlayTime = this.selectionEnd;
                        } else {
                            // 完整播放结束，位置是音频总时长
                            this.lastPlayPosition = this.totalDuration;
                            this.currentPlayTime = this.totalDuration;
                        }
                        this.pauseTime = this.lastPlayPosition;
                        document.getElementById('playBtn').textContent = '播放';
                        document.getElementById('playBtn').disabled = false;
                        this.updatePlayhead(); // 确保播放头重置
                        this.log('⏹️ 播放完成');
                    };


                    // 智能计算播放起始位置
                    let startOffset = this.calculatePlayStartPosition();
                    
                    this.log(`🎯 计算的播放起始位置: ${this.formatTime(startOffset)}`);
                    
                    // 确保起始位置在有效范围内
                    startOffset = Math.max(0, Math.min(startOffset, this.totalDuration));
                    
                    let duration = undefined; // 播放时长（undefined表示播放到结尾）
                     
                    
                    // 如果有選擇範圍，從選擇開始位置播放
                    if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
                        startOffset = this.selectionStart;
                        duration = this.selectionEnd - this.selectionStart;
                        this.audioSource.start(0, startOffset, duration);
                        this.log(`▶️ 播放選擇範圍: ${this.formatTime(startOffset)} - ${this.formatTime(this.selectionEnd)}`);
                    } else {
                        // 播放整個文件
                        this.audioSource.start(0, startOffset);
                        this.log(`▶️ 從位置開始播放: ${this.formatTime(startOffset)}`);
                    }
                    
                    this.startTime = this.audioContext.currentTime - startOffset;
                    this.isPlaying = true;

                    // 更新最后播放位置
                    this.lastPlayPosition = startOffset;
                    // 更新按鈕狀態
                    document.getElementById('playBtn').textContent = '停止';
                    document.getElementById('stopBtn').disabled = false;

                } catch (error) {
                    this.log(`❌ 播放失敗: ${error.message}`);
                    console.error('播放錯誤:', error);
                }
            }
            
            // 智能计算播放起始位置
            calculatePlayStartPosition() {
                // 1. 如果有框选区域，优先从框选开始
                if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
                    this.log('📍 使用框选区域起始位置');
                    return this.selectionStart;
                }
                
                // 2. 如果没有框选，使用播放头当前位置（垂直虚线位置）
                if (this.currentPlayTime > 0 && this.currentPlayTime < this.totalDuration) {
                    this.log('📍 从播放头当前位置开始');
                    return this.currentPlayTime;
                }
                
                // 3. 如果有暂停位置，从暂停位置继续
                if (this.pauseTime > 0) {
                    this.log('📍 从暂停位置继续播放');
                    return this.pauseTime;
                }
                
                // 4. 使用最后播放位置记录
                if (this.lastPlayPosition > 0) {
                    this.log('📍 从最后播放位置开始');
                    return this.lastPlayPosition;
                }
               
                // 5. 尝试使用当前视图的起始位置
                const { startTime } = this.getVisibleTimeRange();
                if (startTime >= 0 && startTime < this.totalDuration) {
                    this.log('📍 从当前视图起始位置开始');
                    return startTime;
                }
                
                // 6. 默认从头开始
                this.log('📍 从头开始播放');
                return 0;
            }
            
            // 旧版本的计算方法（移除重复逻辑）
            calculatePlayStartPositionOld() {
                // 1. 如果有框选区域，从框选开始
                if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
                    this.log('📍 使用框选区域起始位置');
                    return this.selectionStart;
                }
                
                // 2. 如果有暂停位置，从暂停位置继续
                if (this.pauseTime > 0) {
                    this.log('📍 从暂停位置继续播放');
                    return this.pauseTime;
                }
                
                // 3. 如果有最后播放位置记录，使用该位置
                if (this.lastPlayPosition > 0) {
                    this.log('📍 从最后播放位置开始');
                    return this.lastPlayPosition;
                }
                
                // 4. 尝试使用当前视图的起始位置（如果在合理范围内）
                const { startTime } = this.getVisibleTimeRange();
                if (startTime >= 0 && startTime < this.totalDuration) {
                    this.log('📍 从当前视图起始位置开始');
                    return startTime;
                }
                
                // 5. 默认从头开始
                this.log('📍 从头开始播放');
                return 0;
            }
            stopAudio() {
                this.log('⏹️ 执行停止操作...');

                if (this.audioSource && this.isPlaying) {
                    this.audioSource.stop();
                    this.audioSource = null;
                    this.isPlaying = false;
                    
                    // 記錄暫停位置
                    if (this.audioContext) {
                        this.pauseTime = this.audioContext.currentTime - this.startTime;
                        this.pauseTime = Math.max(0, Math.min(this.totalDuration, this.pauseTime));
                        this.lastPlayPosition = this.pauseTime; // 同步更新最后位置
                    }
                    
                    // 更新按鈕狀態
                    document.getElementById('playBtn').textContent = '播放';
                    document.getElementById('playBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    this.updatePlayhead(); // 立即更新播放头位置
                    this.log(`⏸️ 停止播放，位置: ${this.formatTime(this.pauseTime)}`);
                } else {
                    // 重置到開始位置
                    this.pauseTime = this.lastPlayPosition || 0;
                    this.currentPlayTime = this.lastPlayPosition || 0;
                    document.getElementById('playBtn').textContent = '播放';
                    this.log(`⏹️ 重置到位置: ${this.formatTime(this.pauseTime)}`);
                    this.updatePlayhead(); // 重置播放头
                }
            }
            
            updatePlayhead() {
                if (!this.audioData || !this.totalDuration) return;
                
                if (this.isPlaying && this.audioContext) {
                    this.currentPlayTime = this.audioContext.currentTime - this.startTime;
                } else {
                    this.currentPlayTime = this.pauseTime || 0;
                }
                // 确保播放时间在有效范围内
                if (this.totalDuration > 0) {
                    this.currentPlayTime = Math.max(0, Math.min(this.totalDuration, this.currentPlayTime));
                }
                this.currentPlayTime = Math.max(0, Math.min(this.totalDuration, this.currentPlayTime));
                
                // 更新播放頭位置
                const playhead = document.getElementById('playhead');
             
                // 确保播放头元素存在且有必要的数据
                if (!playhead || !this.canvas || !this.totalDuration) {
                    return;
                }
       
                if (playhead && this.canvas && this.pixelsPerSecond > 0) {
                    const { startTime } = this.getVisibleTimeRange();
                    
                    // 确保时间范围有效
                    if (isNaN(startTime) || startTime < 0) {
                        return;
                    }
                   
                    const relativeTime = this.currentPlayTime - startTime;
                    const x = relativeTime * this.pixelsPerSecond;

                    // 设置播放头位置
                    playhead.style.left = x + 'px';
                    // 播放头可见性：稍微扩大可见范围，包括边界位置
                    const isVisible = (x >= -2 && x <= this.canvas.clientWidth + 2);
                    playhead.style.display = isVisible ? 'block' : 'none';
                    
                    // 播放结束时确保播放头在正确位置
                    if (!this.isPlaying && this.currentPlayTime >= this.totalDuration) {
                        // 播放结束，播放头应该在音频结束位置
                        const endX = (this.totalDuration - startTime) * this.pixelsPerSecond;
                        playhead.style.left = endX + 'px';
                        playhead.style.display = (endX >= -2 && endX <= this.canvas.clientWidth + 2) ? 'block' : 'none';

                        console.log(`播放头超出视图: x=${x}, 当前时间=${this.currentPlayTime.toFixed(3)}, 视图起始=${startTime.toFixed(3)}`);
                    }
                }
                
                // 更新時間顯示
                document.getElementById('timeDisplay').textContent = this.formatTime(this.currentPlayTime);
            }
            
            // 点击播放头设置播放位置
            setupPlayheadInteraction() {
                const playhead = document.getElementById('playhead');
                const waveformContainer = document.querySelector('.waveform-container');
                
                // 点击波形区域设置播放位置
                waveformContainer.addEventListener('click', (e) => {
                    if (!this.audioData || this.isPlaying) return;

                    // 如果正在拖拽选择，不处理点击
                    if (e.target.closest('.selection-box')) return;

                    // 如果刚完成选择（在很短时间内），不处理点击以避免清除选择
                    if (this.justFinishedSelection) {
                        this.log(`🚫 刚完成选择，忽略点击事件`);
                        return;
                    }

                    const rect = waveformContainer.getBoundingClientRect();
                    const clickX = e.clientX - rect.left;

                    // 计算点击位置对应的时间
                    const { startTime } = this.getVisibleTimeRange();
                    const timePerPx = 1 / this.pixelsPerSecond;
                    const clickTime = startTime + clickX * timePerPx;

                    // 确保时间在有效范围内
                    if (clickTime >= 0 && clickTime <= this.totalDuration) {
                        this.currentPlayTime = clickTime;
                        this.lastPlayPosition = clickTime;
                        this.pauseTime = clickTime;

                        // 清除选择区域
                        this.selectionStart = 0;
                        this.selectionEnd = 0;
                        document.getElementById('selectionBox').style.display = 'none';
                        // 清除保存的选择状态，防止缩放时重新出现
                        this.lastSelectionState = null;
                        this.resetSelection();

                        this.updatePlayhead();
                        this.log(`🎯 设置播放位置: ${this.formatTime(clickTime)}`);

                        // 记录点击后的状态，用于调试缩放问题
                        this.lastClickTime = Date.now();
                        this.log(`🔍 点击后状态记录: viewOffset=${this.viewOffset.toFixed(1)}, 时间=${Date.now()}`);
                    }
                });
            }                       
            // 在选择更新时也更新播放位置
            updateSelection(startPx, endPx) {
                const { startTime } = this.getVisibleTimeRange();
                const timePerPx = 1 / this.pixelsPerSecond;

                // 像素位置转换为时间（startPx和endPx已经是相对于容器的位置）
                this.selectionStart = startTime + startPx * timePerPx;
                this.selectionEnd = startTime + endPx * timePerPx;

                // 确保选择范围在音频范围内
                this.selectionStart = Math.max(0, Math.min(this.totalDuration, this.selectionStart));
                this.selectionEnd = Math.max(0, Math.min(this.totalDuration, this.selectionEnd));

                // 调试信息：显示选择框的时间范围
                this.log(`📦 创建选择框: 时间${this.selectionStart.toFixed(3)}s-${this.selectionEnd.toFixed(3)}s, 像素${startPx.toFixed(1)}-${endPx.toFixed(1)}px, 缩放${this.pixelsPerSecond.toFixed(1)}px/s`);

                // 更新最后播放位置为选择开始位置
                if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
                    this.lastPlayPosition = this.selectionStart;
                    this.log(`📍 框选更新，播放起始位置: ${this.formatTime(this.lastPlayPosition)}`);
                }

                // 更新显示
                document.getElementById('selBegin').textContent = this.formatTime(this.selectionStart);
                document.getElementById('selEnd').textContent = this.formatTime(this.selectionEnd);
                document.getElementById('selLength').textContent = this.formatTime(this.selectionEnd - this.selectionStart);
            }
            // 修复播放控制按钮状态
            updatePlaybackButtonStates() {
                const playBtn = document.getElementById('playBtn');
                const stopBtn = document.getElementById('stopBtn');
                
                if (this.isPlaying) {
                    playBtn.textContent = '停止';
                    playBtn.disabled = false;
                    stopBtn.disabled = false;
                } else {
                   playBtn.textContent = '播放';
                    stopBtn.disabled = true;
                }
            }

          
            // 修改滑块事件处理，确保不超出限制
            setupZoomSliderLimits() {
                const slider = document.getElementById('zoomSlider');
                if (slider) {
                    // 计算动态最小值
                    const dynamicMinIndex = this.calculateDynamicMinZoom();

                    // 重新设置滑块范围，使用动态最小值
                    slider.min = this.zoomSteps[dynamicMinIndex];
                    slider.max = this.zoomSteps[this.zoomSteps.length - 1];

                    // 确保当前值在范围内
                    if (parseFloat(slider.value) < parseFloat(slider.min)) {
                        slider.value = slider.min;
                        this.setZoomLevel(parseFloat(slider.min));
                    }
                }
            }
            // 固定阶数缩放方法
            // 縮放控制
            zoomIn() {
                // 保存当前选择状态
                this.saveSelectionState();

                if (this.currentZoomIndex < this.zoomSteps.length - 1) {
                    this.currentZoomIndex++;
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                    this.log(`🔍 缩放级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length} (${this.zoomLevel}x)`);
                    document.getElementById('zoomSlider').value = this.zoomLevel;
                    this.updateZoom();

                    // 恢复选择状态
                    this.restoreSelectionState();
                }
            }
            
            zoomOut() {
                // 保存当前选择状态
                this.saveSelectionState();

                // 动态计算最小缩放级别
                const dynamicMinIndex = this.calculateDynamicMinZoom();

                // 检查是否已达到动态计算的最小缩放级别
                if (this.currentZoomIndex <= dynamicMinIndex) {
                    return;
                }

                if (this.currentZoomIndex > 0) {
                    // 额外检查：确保不超出动态最小值
                    if (this.currentZoomIndex - 1 < dynamicMinIndex) {
                        return;
                    }
                    this.currentZoomIndex--;
                    this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                    this.updatePixelsPerSecond();
                    this.log(`🔍 缩放级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length} (${this.zoomLevel}x)`);
                    document.getElementById('zoomSlider').value = this.zoomLevel;
                    this.updateZoom();

                    // 恢复选择状态
                    this.restoreSelectionState();
                }

                // 如果zoom out到最小級別，重置視角
                if (this.currentZoomIndex <= dynamicMinIndex) {
                    this.viewOffset = 0;
                }
            }
            
           
            // 设置缩放到指定级别
            setZoomLevel(targetZoom) {
                // 确保targetZoom是有效数值
                if (isNaN(targetZoom) || !isFinite(targetZoom)) {
                    console.warn('Invalid targetZoom:', targetZoom);
                    return;
                }
                // 找到最接近的缩放级别
                let closestIndex = 0;
                let minDiff = Math.abs(this.zoomSteps[0] - targetZoom);
                
                for (let i = 1; i < this.zoomSteps.length; i++) {
                    const diff = Math.abs(this.zoomSteps[i] - targetZoom);
                    if (diff < minDiff) {
                        minDiff = diff;
                        closestIndex = i;
                    }
                }
                
                this.currentZoomIndex = closestIndex;
                this.zoomLevel = this.zoomSteps[closestIndex];
                this.updatePixelsPerSecond();
                this.updateZoom();
            }
            // 缩放操作时智能调整视图位置
            preservePlayPositionOnZoom() {

                if (!this.canvas || !this.totalDuration) return;
                
                const containerWidth = this.canvas.clientWidth;
                
                // 1. 如果有选择区域，优先保持选择区域可见
                if (this.selectionStart > 0 && this.selectionEnd > this.selectionStart) {
 
                    // 确保整个选择区域可见
                    const selectionStartX = this.selectionStart * this.pixelsPerSecond;
                    const selectionEndX = this.selectionEnd * this.pixelsPerSecond;
                    const selectionWidth = selectionEndX - selectionStartX;
                    
                    if (selectionWidth <= containerWidth) {
                        // 选择区域能完全显示，居中显示
                        const selectionCenter = (selectionStartX + selectionEndX) / 2;
                        this.viewOffset = Math.max(0, selectionCenter - containerWidth / 2);
                    } else {
                        // 选择区域太大，显示开始部分
                        this.viewOffset = Math.max(0, selectionStartX);
                    }
                    this.log(`🔍 缩放后调整视图以显示选择区域`);
                }

                // 2. 如果有播放位置，尝试保持其可见
                else if (this.currentPlayTime > 0 || this.lastPlayPosition > 0) {
                    const playTime = this.currentPlayTime || this.lastPlayPosition;
                    const playPositionX = playTime * this.pixelsPerSecond;
                    
                    // 如果播放位置不在当前视图中，调整视图
                    if (playPositionX < this.viewOffset || playPositionX > this.viewOffset + containerWidth) {
                        this.viewOffset = Math.max(0, playPositionX - containerWidth / 2);
                        this.log(`🔍 缩放后调整视图以显示播放位置`);
                    }
                }
            }

            amplitudeZoomIn() {
                this.amplitudeZoom = Math.min(this.maxAmplitudeZoom, this.amplitudeZoom * 1.5);
                document.getElementById('ampSlider').value = this.amplitudeZoom;
                document.getElementById('ampSlider').max = this.maxAmplitudeZoom;
                this.updateWaveformInfo();
                this.updateAmplitudeAxis();
            }
            
            amplitudeZoomOut() {
                this.amplitudeZoom = Math.max(1, this.amplitudeZoom / 1.5);
                document.getElementById('ampSlider').value = this.amplitudeZoom;
                this.updateWaveformInfo();
                this.updateAmplitudeAxis();
            }
            // 设置高度调整器
            setupHeightAdjuster() {
                // 在waveform区域底部添加高度调整条
                const mainArea = document.querySelector('.main-area');
                const heightAdjuster = document.createElement('div');
                heightAdjuster.className = 'height-adjuster';
                heightAdjuster.id = 'heightAdjuster';
                
                // 插入到main-area的最后
                mainArea.appendChild(heightAdjuster);
                
                let isResizing = false;
                let startY = 0;
                let startHeight = 0;
                
                heightAdjuster.addEventListener('mousedown', (e) => {
                    isResizing = true;
                    this.isResizingHeight = true;
                    startY = e.clientY;
                    startHeight = mainArea.offsetHeight;
                    document.body.style.cursor = 'ns-resize';
                    e.preventDefault();
                    
                    this.log('📏 开始调整波形区域高度');
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (!isResizing) return;
                    
                    const deltaY = e.clientY - startY;
                    const containerHeight = document.querySelector('.test-container').offsetHeight;
                    const newHeight = startHeight + deltaY;
                    const minHeight = 200; // 最小高度
                    const maxHeight = containerHeight * 0.7; // 最大70%
                    
                    const clampedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
                    const percentage = (clampedHeight / containerHeight) * 100;
                    
                    mainArea.style.flex = `0 0 ${clampedHeight}px`;
                    mainArea.style.maxHeight = `${clampedHeight}px`;
                });
                
                document.addEventListener('mouseup', () => {
                    if (isResizing) {
                        isResizing = false;
                        this.isResizingHeight = false;
                        document.body.style.cursor = '';
                        this.updateLayout(); // 重新调整整体布局
                        this.log('📏 波形区域高度调整完成');
                    }
                });
                
                // 双击重置高度
                heightAdjuster.addEventListener('dblclick', () => {
                    const containerHeight = document.querySelector('.test-container').offsetHeight;
                    const defaultHeight = containerHeight * 0.45; // 45%
                    mainArea.style.flex = `0 0 ${defaultHeight}px`;
                    mainArea.style.maxHeight = `${defaultHeight}px`;
                    this.updateLayout();
                    this.log('📏 重置波形区域高度为默认值');
                });
            }
                           
            // 设置纵轴交互
            setupAmplitudeAxisInteraction() {
                const amplitudeAxis = document.getElementById('amplitudeAxis');
                let isDragging = false;
                let lastMouseY = 0;
                
                amplitudeAxis.addEventListener('mouseenter', () => {
                    if (!isDragging) {
                        amplitudeAxis.style.cursor = 'grab';
                    }
                });
                
                amplitudeAxis.addEventListener('mouseleave', () => {
                    if (!isDragging) {
                        amplitudeAxis.style.cursor = 'default';
                    }
                });
                
                amplitudeAxis.addEventListener('mousedown', (e) => {
                    if (!this.audioData) return;
                    if (e.button !== 0) return; // 只响应左键

                    // 在頻譜圖模式下禁用拖拽
                    if (this.displayMode === 'spectrogram') {
                        return;
                    }

                    isDragging = true;
                    lastMouseY = e.clientY;
                    amplitudeAxis.style.cursor = 'grabbing';
                    e.preventDefault();
                    e.stopPropagation();

                    this.log('📊 开始纵轴拖拽');
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (!isDragging || !this.audioData) return;

                    // 在頻譜圖模式下禁用拖拽
                    if (this.displayMode === 'spectrogram') {
                        return;
                    }

                    const deltaY = e.clientY - lastMouseY;
                    const sensitivity = 0.01; // 拖拽灵敏度

                    // 根据当前缩放级别调整偏移量
                    const offsetChange = deltaY * sensitivity / (this.amplitudeZoom / 10);
                    this.amplitudeOffset += offsetChange;

                    // 限制偏移范围（防止拖得太远）
                    const maxOffset = 2.0; // 最大偏移为2倍振幅范围
                    this.amplitudeOffset = Math.max(-maxOffset, Math.min(maxOffset, this.amplitudeOffset));

                    this.updateAmplitudeAxis();
                    lastMouseY = e.clientY;
                });
                
                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        const amplitudeAxis = document.getElementById('amplitudeAxis');
                        
                        if (amplitudeAxis.matches(':hover')) {
                            amplitudeAxis.style.cursor = 'grab';
                        } else {
                            amplitudeAxis.style.cursor = 'default';
                        }
                        
                        this.log(`📊 纵轴拖拽结束，偏移: ${this.amplitudeOffset.toFixed(3)}`);
                    }
                });
                
                // 纵轴滚轮缩放
                amplitudeAxis.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const delta = e.deltaY > 0 ? -1 : 1;
                    
                   
                    if (delta > 0) {
                        this.amplitudeZoomIn();
                    } else {
                        this.amplitudeZoomOut();
                    }
                });
                
                // 双击重置偏移
                amplitudeAxis.addEventListener('dblclick', () => {
                    this.amplitudeOffset = 0;
                    this.updateAmplitudeAxis();
                    this.log('📊 重置纵轴偏移');
                });
            }
                        
            updateZoom() {
                
                this.updateViewOffset();
                this.clampViewToAudioBounds(); // 確保不超出音頻範圍
                this.preservePlayPositionOnZoom(); // 保持播放位置可见
                this.updateTimeline();
                this.updateWaveformInfo();
            }
            
            updateWaveformInfo() {
                const info = document.getElementById('waveformInfo');
                let unitText = '';
                switch (this.amplitudeUnit) {
                    case 'linear': unitText = '線性'; break;
                    case 'db': unitText = 'dB'; break;
                    case 'percent': unitText = '%'; break;
                }
                
                 const showingSamples = this.zoomLevel > this.sampleLevelThreshold;
                const modeText = showingSamples ? ' | 样本模式' : '';
                const levelInfo = ` | 级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length}`;
                const offsetInfo = this.amplitudeOffset !== 0 ? ` | 偏移: ${this.amplitudeOffset.toFixed(3)}` : '';
                 
                info.textContent = `时间轴: ${this.zoomLevel}:1 | 振幅: ${this.amplitudeZoom.toFixed(1)}x | 单位: ${unitText}${modeText}${levelInfo}${offsetInfo}`;
           }
            
            updateTimeline() {
                const timeline = document.getElementById('timeline');
                timeline.innerHTML = '';
                
                // 确保基本状态有效
                if (isNaN(this.pixelsPerSecond) || this.pixelsPerSecond <= 0) {
                    this.updatePixelsPerSecond();
                }
                if (!this.totalDuration || !this.canvas || !timeline) return;
                
                const containerWidth = timeline.offsetWidth;
                if (containerWidth === 0) return;

                // 強制重新計算視角範圍
                this.updateViewOffset();
                this.clampViewToAudioBounds();
                
                const { startTime, endTime } = this.getVisibleTimeRange();
                
                // 验证时间范围有效性
                if (isNaN(startTime) || isNaN(endTime) || endTime <= startTime) {
                    console.warn('Invalid time range:', { startTime, endTime, pixelsPerSecond: this.pixelsPerSecond, viewOffset: this.viewOffset });
                    return;
                }
                // 如果時間範圍無效，返回
                if (endTime <= startTime) return;
                
                const visibleDuration = endTime - startTime;
                const pixelsPerSecond = containerWidth / visibleDuration;
                
                // 檢查是否顯示整個音頻範圍（容差0.01秒）
                const isShowingFullAudio = (startTime <= 0.001 && endTime >= this.totalDuration - 0.001) ||
                                       (this.currentZoomIndex <= 0);
         
                
                console.log(`Timeline Debug: startTime=${startTime.toFixed(3)}, endTime=${endTime.toFixed(3)}, totalDuration=${this.totalDuration.toFixed(3)}, isShowingFullAudio=${isShowingFullAudio}, zoomLevel=${this.zoomLevel}`);
                
                // 計算合適的時間間隔
                const minPixelsBetweenLabels = 80;
                const referenceTime = isShowingFullAudio ? this.totalDuration : visibleDuration;
                const idealInterval = referenceTime / (containerWidth / minPixelsBetweenLabels);
                
                // 標準時間間隔選項
                const intervals = [
                    0.000001, 0.000002, 0.000005, // 微秒級
                    0.00001, 0.00002, 0.00005,
                    0.0001, 0.0002, 0.0005,
                    0.001, 0.002, 0.005,          // 毫秒級
                    0.01, 0.02, 0.05,
                    0.1, 0.2, 0.5,                // 百毫秒級
                    1, 2, 5,                      // 秒級
                    10, 20, 30,
                    60, 120, 300,                 // 分鐘級
                    600, 1200, 1800
                ];
                
                let majorInterval = intervals[intervals.length - 1]; // 默認最大間隔
                for (let i = 0; i < intervals.length; i++) {
                    if (intervals[i] * pixelsPerSecond >= minPixelsBetweenLabels) {
                        majorInterval = intervals[i];
                        break;
                    }
                }
                
                // 對於全音頻顯示，確保有合適數量的刻度
                if (isShowingFullAudio) {
                    const numTicks = this.totalDuration / majorInterval;
                    if (numTicks < 3) {
                        majorInterval = this.totalDuration / 5; // 至少5個刻度
                    } else if (numTicks > 20) {
                        majorInterval = this.totalDuration / 10; // 最多10個刻度
                    }
                }
                
                const minorInterval = majorInterval / 5;
                
                // 動態精度
                let precision = 0;
                if (majorInterval < 0.001) precision = 6;
                else if (majorInterval < 0.01) precision = 4;
                else if (majorInterval < 0.1) precision = 3;
                else if (majorInterval < 1) precision = 2;
                else if (majorInterval < 10) precision = 1;
                else precision = 0;
                
                // 計算刻度起始和結束位置
                let tickStart, tickEnd;
                if (isShowingFullAudio) {
                    // 顯示全部音頻：從0開始到totalDuration結束
                    tickStart = 0;
                    tickEnd = this.totalDuration;
                } else {
                    // 部分顯示：基於可見範圍
                    tickStart = Math.floor(startTime / majorInterval) * majorInterval;
                    tickEnd = endTime + majorInterval;
                }
                
                console.log(`Tick range: ${tickStart.toFixed(3)} to ${tickEnd.toFixed(3)}, interval: ${majorInterval.toFixed(6)}`);
                
                // 繪製主要時間標籤
                for (let time = tickStart; time <= tickEnd; time += majorInterval) {
                    // 確保不超出音頻範圍
                    if (time > this.totalDuration + 0.001) break;
                    
                    const x = (time - startTime) * pixelsPerSecond;
                    
                    if (x >= -20 && x <= containerWidth + 20) {
                        const marker = document.createElement('div');
                        marker.className = 'timeline-marker major';
                        marker.style.left = x + 'px';
                        marker.textContent = time.toFixed(precision);
                        timeline.appendChild(marker);
                        
                        console.log(`Major tick at time=${time.toFixed(3)}, x=${x.toFixed(1)}`);
                    }
                }
                
                // 繪製次要刻度線
                const minorStart = isShowingFullAudio ? 0 : Math.floor(startTime / minorInterval) * minorInterval;
                
                for (let time = minorStart; time <= tickEnd; time += minorInterval) {
                    if (time > this.totalDuration + 0.001) break;
                    
                    const x = (time - startTime) * pixelsPerSecond;
                    
                    // 確保不超出音頻範圍
                    if (time > this.totalDuration + 0.001) break;
 

                    if (x >= 0 && x <= containerWidth) {
                        const isNearMajorTick = Math.abs((time - tickStart) % majorInterval) < minorInterval * 0.1;
                        
                        const tick = document.createElement('div');
                        tick.className = `timeline-tick ${isNearMajorTick ? 'major' : 'minor'}`;
                        tick.style.left = x + 'px';
                        tick.style.height = isNearMajorTick ? '15px' : '8px';
                        timeline.appendChild(tick);
                    }
                }
            }

            
            selectAll() {
                const container = document.querySelector('.waveform-container');
                const selectionBox = document.getElementById('selectionBox');
                
                selectionBox.style.display = 'block';
                selectionBox.style.left = '0px';
                selectionBox.style.width = container.offsetWidth + 'px';
                selectionBox.style.top = '0px';
                selectionBox.style.height = '100%';
                
                this.updateSelection(0, container.offsetWidth);
            }
            
            formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                const ms = Math.floor((seconds % 1) * 1000);
                return `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
            }
            
            // 繪圖功能

            setupResizeHandle() {
                const resizeHandle = document.getElementById('resizeHandle');
                const mainArea = document.querySelector('.main-area');
                const container = document.querySelector('.test-container');
                
                let isResizing = false;
                let startY = 0;
                let startHeight = 0;
                
                resizeHandle.addEventListener('mousedown', (e) => {
                    isResizing = true;
                    startY = e.clientY;
                    startHeight = mainArea.offsetHeight;
                    document.body.style.cursor = 'ns-resize';
                    e.preventDefault();
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (!isResizing) return;
                    
                    const deltaY = e.clientY - startY;
                    const containerHeight = container.offsetHeight;
                    const newHeight = startHeight + deltaY;
                    const minHeight = 150; // 最小高度
                    const maxHeight = containerHeight - 280; // 留空間給時間軸和底部面板
                    
                    const clampedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
                    const percentage = (clampedHeight / containerHeight) * 100;
                    
                    mainArea.style.flex = `0 0 ${percentage}%`;
                });
                
                document.addEventListener('mouseup', () => {
                    if (isResizing) {
                        isResizing = false;
                        document.body.style.cursor = '';
                        this.updateLayout(); // 重新調整整體佈局
                    }
                });
            }
            drawPlaceholder() {
                this.ctx.fillStyle = '#000';
                this.ctx.fillRect(0, 0, this.canvas.clientWidth, this.canvas.clientHeight);
                
                this.ctx.strokeStyle = '#444';
                this.ctx.setLineDash([5, 5]);
                this.ctx.beginPath();
                this.ctx.moveTo(0, this.canvas.clientHeight / 2);
                this.ctx.lineTo(this.canvas.clientWidth, this.canvas.clientHeight / 2);
                this.ctx.stroke();
                
                this.ctx.fillStyle = '#888';
                this.ctx.font = '16px Courier New';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('等待WAV文件...', this.canvas.clientWidth / 2, this.canvas.clientHeight / 2 - 10);
            }
            
            drawWaveform() {
                if (!this.audioData) {
                    this.drawPlaceholder();
                    return;
                }

                // 根據顯示模式選擇繪製方法
                if (this.displayMode === 'spectrogram') {
                    this.updateSpectrogramDisplay();
                    return;
                }

                const width = this.canvas.clientWidth;
                const height = this.canvas.clientHeight;
                
                // 清除畫布
                this.ctx.fillStyle = '#000';
                this.ctx.fillRect(0, 0, width, height);
                
                // 繪製網格
                this.drawGrid(width, height);
                
                // 繪製波形
                this.drawWaveformData(width, height);
                
                // 繪製零線
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 1;
                this.ctx.setLineDash([]);
                this.ctx.beginPath();
                 const zeroY = height / 2 - (this.amplitudeOffset * height / 2 * (this.amplitudeZoom / 10) * 0.9);
                this.ctx.moveTo(0, zeroY);
                this.ctx.lineTo(width, zeroY);
                this.ctx.stroke();
            }
            
            drawGrid(width, height) {
                this.ctx.strokeStyle = '#222';
                this.ctx.lineWidth = 0.5;
                this.ctx.setLineDash([]);
                
                // 水平網格線（振幅） - 使用顯眼顏色和依據單位調整
                const amplitudeScale = (this.amplitudeZoom / 10) * 0.9;
                let ampSteps = [];
                
                switch (this.amplitudeUnit) {
                    case 'db':
                        ampSteps = [-60, -40, -20, -10, -6, -3, -1, 0, 3, 6];
                        break;
                    case 'percent':
                        ampSteps = [-100, -75, -50, -25, 0, 25, 50, 75, 100];
                        break;
                    case 'linear':
                    default:
                        ampSteps = [-1, -0.75, -0.5, -0.25, 0, 0.25, 0.5, 0.75, 1];
                        break;
                }
                
                ampSteps.forEach(amp => {
                    let normalizedValue = amp;
                    if (this.amplitudeUnit === 'db') {
                        // dB范围从-60到+20，总共80dB范围，映射到-1到1
                        normalizedValue = (amp + 60) / 80 * 2 - 1;
                    }
                    if (this.amplitudeUnit === 'percent') normalizedValue = amp / 100;
                    
                    const y = height / 2 - ((normalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);

                    // 特殊線條顏色
                    if ((this.amplitudeUnit === 'db' && amp === 0) ||
                        (this.amplitudeUnit === 'linear' && amp === 0) ||
                        (this.amplitudeUnit === 'percent' && amp === 0)) {
                        this.ctx.strokeStyle = '#ffffff'; // 白線
                        this.ctx.lineWidth = 1;
                    } else {
                        this.ctx.strokeStyle = '#750000'; // 紅色網格線
                        this.ctx.lineWidth = 0.8;
                   }
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(width, y);
                   this.ctx.stroke();
                });
                
                // 垂直網格線（時間）
                if (this.totalDuration > 0) {
                    const { startTime, endTime } = this.getVisibleTimeRange();
                    const timespan = endTime - startTime;
                    const pixelsPerSecond = width / timespan;
                    
                    let interval = 1;
                    while (interval * pixelsPerSecond < 50) {
                        interval *= 2;
                    }
                    
                    for (let time = Math.floor(startTime / (interval / 4)) * (interval / 4); time <= endTime; time += interval / 4) {
                        const x = (time - startTime) * pixelsPerSecond;
                        const isMajor = (Math.abs(time % interval) < 0.001);
                        
                        if (x >= 0 && x <= width) {
                            this.ctx.strokeStyle = isMajor ? '#750000' : '#222';
                            this.ctx.beginPath();
                            this.ctx.moveTo(x, 0);
                            this.ctx.lineTo(x, height);
                            this.ctx.stroke();
                        }
                    }
                }
                

            }
            
            drawWaveformData(width, height) {
                const { startTime, endTime } = this.getVisibleTimeRange();
                const startSample = Math.floor(startTime * this.sampleRate);
                const endSample = Math.min(Math.floor(endTime * this.sampleRate), this.audioData.length);
                
                const samplesPerPixel = (endSample - startSample) / width;
                const amplitudeScale = (this.amplitudeZoom / 10) * 0.9;
                
                // 判斷是否需要顯示樣本點
                const showSamplePoints = this.zoomLevel > this.sampleLevelThreshold && samplesPerPixel <= 1;

                if (showSamplePoints) {
                  this.drawSampleLevelWaveform(width, height, startSample, endSample, amplitudeScale);
                } else {
                    this.drawStandardWaveform(width, height, startSample, endSample, samplesPerPixel, amplitudeScale);
                }
            }

            // 樣本級別繪製（顯示每個樣本點）
            drawSampleLevelWaveform(width, height, startSample, endSample, amplitudeScale) {
                const sampleWidth = width / (endSample - startSample);
                
                // 当每个样本占用的像素超过2时，显示样本格子
                const showSampleGrid = sampleWidth >= 2;
 


                // 繪製連接線
                this.ctx.strokeStyle = '#00ff88';
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();
                
                let hasStarted = false;
                
                for (let i = startSample; i < endSample; i++) {
                    if (i >= this.audioData.length) break;
                    
                    const x = (i - startSample) * sampleWidth;
                    const sample = this.applyAmplitudeScaling(this.audioData[i]);
                    const y = height / 2 - ((sample + this.amplitudeOffset) * height / 2 * amplitudeScale);

                    if (!hasStarted) {
                        this.ctx.moveTo(x, y);
                        hasStarted = true;
                    } else {
                        this.ctx.lineTo(x, y);
                    }
                }
                this.ctx.stroke();
                // 绘制样本格子（当缩放足够大时）
                if (showSampleGrid) {
                    this.ctx.strokeStyle = '#444';
                    this.ctx.lineWidth = 0.5;
                    
                    // 垂直格子线
                    for (let i = startSample; i <= endSample; i++) {
                        const x = (i - startSample) * sampleWidth;
                        this.ctx.beginPath();
                        this.ctx.moveTo(x, 0);
                        this.ctx.lineTo(x, height);
                        this.ctx.stroke();
                    }
                }
             
                // 繪製樣本點（方塊）
                this.ctx.fillStyle = '#00ff88';
                const pointSize = Math.max(2, Math.min(8, sampleWidth * 0.6)); // 动态调整点大小

                for (let i = startSample; i < endSample; i++) {
                    if (i >= this.audioData.length) break;
                    
                    const x = (i - startSample) * sampleWidth;
                    const sample = this.applyAmplitudeScaling(this.audioData[i]);
                    const y = height / 2 - ((sample + this.amplitudeOffset) * height / 2 * amplitudeScale);

                    
                    // 繪製方塊樣本點
                    this.ctx.fillRect(
                        x - pointSize / 2, 
                        y - pointSize / 2, 
                        pointSize, 
                        pointSize
                    );
                    
    
                    // 移除自动显示的样本索引标签，改为鼠标悬停时显示
                }
            }

            // 標準波形繪製（原有邏輯）
            drawStandardWaveform(width, height, startSample, endSample, samplesPerPixel, amplitudeScale) {
                this.ctx.strokeStyle = '#00ff88';
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();
                
                let hasStarted = false;
                
                for (let x = 0; x < width; x++) {
                    const sampleIdx = startSample + Math.floor(x * samplesPerPixel);
                    const sampleEndIdx = Math.min(sampleIdx + Math.ceil(samplesPerPixel), endSample);
                    
                    if (sampleEndIdx <= sampleIdx || sampleIdx >= this.audioData.length) continue;
                    
                    let min = this.audioData[sampleIdx];
                    let max = this.audioData[sampleIdx];
                    
                    // 計算該像素範圍內的最大最小值
                    for (let i = sampleIdx; i < sampleEndIdx && i < this.audioData.length; i++) {
                        min = Math.min(min, this.audioData[i]);
                        max = Math.max(max, this.audioData[i]);
                    }
                    
                    min = this.applyAmplitudeScaling(min);
                    max = this.applyAmplitudeScaling(max);
                    

                    const yMin = height / 2 - ((min + this.amplitudeOffset) * height / 2 * amplitudeScale);
                    const yMax = height / 2 - ((max + this.amplitudeOffset) * height / 2 * amplitudeScale);
    
                    if (!hasStarted) {
                        this.ctx.moveTo(x, (yMin + yMax) / 2);
                        hasStarted = true;
                    } else {
                        if (Math.ceil(samplesPerPixel) > 1) {
                            // 繪製最大最小值範圍
                            this.ctx.lineTo(x, yMax);
                            this.ctx.lineTo(x, yMin);
                        } else {
                            // 單樣本或接近單樣本
                            this.ctx.lineTo(x, (yMin + yMax) / 2);
                        }
                    }
                }
                
                this.ctx.stroke();
            }
            
            applyAmplitudeScaling(value) {
                switch (this.amplitudeUnit) {
                    case 'db':
                        // dB模式下保持原始波形值，不进行dB转换
                        // 波形数据本身保持-1到1的范围，刻度轴显示dB值
                        return value;
                    case 'percent':
                        return value;
                    case 'linear':
                    default:
                        return value;
                }
            }
            
            drawSpectrum() {
                const canvas = this.spectrumCanvas;
                const ctx = this.spectrumCtx;
                const width = canvas.clientWidth - 40;
                const height = canvas.clientHeight;
                
                ctx.fillStyle = '#000';
                ctx.fillRect(40, 0, width, height);
                
                if (this.spectrumData.length === 0) return;
                
                const barWidth = width / (this.spectrumData.length / 2);
                
                for (let i = 0; i < this.spectrumData.length / 2; i++) {
                    const amplitude = this.spectrumData[i] / 255;
                    const barHeight = amplitude * height;
                    const x = 40 + i * barWidth;
                    const y = height - barHeight;
                    
                    const hue = (1 - i / (this.spectrumData.length / 2)) * 240;
                    const saturation = 80 + amplitude * 20;
                    const lightness = 30 + amplitude * 50;
                    
                    ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                    ctx.fillRect(x, y, Math.max(1, barWidth - 0.5), barHeight);
                }
            }
            
            updateLevelMeters() {
                if (!this.audioData || this.audioData.length === 0) return;
                
                // 計算當前視圖的音頻數據
                const { startTime, endTime } = this.getVisibleTimeRange();
                const startSample = Math.floor(startTime * this.sampleRate);
                const endSample = Math.min(Math.floor(endTime * this.sampleRate), this.audioData.length);
                
                let rms = 0;
                let peak = 0;
                
                for (let i = startSample; i < endSample; i++) {
                    if (i < this.audioData.length) {
                        const sample = Math.abs(this.audioData[i]);
                        rms += sample * sample;
                        peak = Math.max(peak, sample);
                    }
                }
                
                rms = Math.sqrt(rms / (endSample - startSample));
                
                const rmsDb = rms > 0 ? 20 * Math.log10(rms) : -60;
                const peakDb = peak > 0 ? 20 * Math.log10(peak) : -60;
                
                const volumePercent = Math.max(0, (rmsDb + 60) / 60 * 100);
                const peakPercent = Math.max(0, (peakDb + 60) / 60 * 100);
                
                document.getElementById('volumeBar').style.width = volumePercent + '%';
                document.getElementById('peakBar').style.width = peakPercent + '%';
                
                document.getElementById('volumeText').textContent = rmsDb.toFixed(1);
                document.getElementById('peakText').textContent = peakDb.toFixed(1);
            }

            // 顯示模式切換
            setDisplayMode(mode) {
                this.displayMode = mode;

                const waveformCanvas = document.getElementById('waveformCanvas');
                const spectrogramCanvas = document.getElementById('spectrogramCanvas');
                const waveformBtn = document.getElementById('waveformModeBtn');
                const spectrogramBtn = document.getElementById('spectrogramModeBtn');

                if (mode === 'waveform') {
                    waveformCanvas.style.display = 'block';
                    spectrogramCanvas.style.display = 'none';
                    waveformBtn.classList.add('active');
                    spectrogramBtn.classList.remove('active');
                    this.log('切換到波形顯示模式');
                } else if (mode === 'spectrogram') {
                    waveformCanvas.style.display = 'none';
                    spectrogramCanvas.style.display = 'block';
                    waveformBtn.classList.remove('active');
                    spectrogramBtn.classList.add('active');
                    this.log('切換到頻譜圖顯示模式');

                    // 如果有音頻數據，生成頻譜圖
                    if (this.audioData) {
                        this.generateSpectrogram();
                    }
                }

                this.updateDisplay();
            }

            // 生成頻譜圖數據
            generateSpectrogram() {
                if (!this.audioData) {
                    this.log('❌ 無音頻數據，無法生成頻譜圖');
                    return;
                }

                this.log('🎵 開始生成頻譜圖數據...');

                const canvas = document.getElementById('spectrogramCanvas');
                if (!canvas) {
                    this.log('❌ 找不到頻譜圖canvas元素');
                    return;
                }

                const ctx = canvas.getContext('2d');

                // 設置canvas尺寸
                const container = canvas.parentElement;
                if (!container) {
                    this.log('❌ 找不到canvas容器');
                    return;
                }

                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;

                this.log(`📐 Canvas尺寸: ${canvas.width}x${canvas.height}`);

                const width = canvas.width;
                const height = canvas.height;

                if (width === 0 || height === 0) {
                    this.log('❌ Canvas尺寸無效');
                    return;
                }

                // FFT參數
                const fftSize = this.fftSize;
                const hopSize = this.hopSize;
                const numFrames = Math.floor((this.audioData.length - fftSize) / hopSize) + 1;
                const numBins = fftSize / 2;

                this.log(`🔧 FFT參數: fftSize=${fftSize}, hopSize=${hopSize}, frames=${numFrames}, bins=${numBins}`);

                // 創建頻譜圖數據
                const spectrogramData = new Array(numFrames);

                for (let frame = 0; frame < numFrames; frame++) {
                    const startSample = frame * hopSize;
                    const frameData = this.audioData.slice(startSample, startSample + fftSize);

                    // 如果幀數據不足，用零填充
                    while (frameData.length < fftSize) {
                        frameData.push(0);
                    }

                    // 應用窗函數（漢明窗）
                    for (let i = 0; i < fftSize; i++) {
                        frameData[i] *= 0.54 - 0.46 * Math.cos(2 * Math.PI * i / (fftSize - 1));
                    }

                    // 執行FFT
                    const spectrum = this.fft(frameData);
                    spectrogramData[frame] = spectrum;

                    // 進度報告
                    if (frame % 100 === 0) {
                        this.log(`📊 處理進度: ${Math.floor(frame / numFrames * 100)}%`);
                    }
                }

                this.spectrogramData = spectrogramData;
                this.log('✅ 頻譜圖數據生成完成，開始繪製...');

                this.drawSpectrogram(ctx, width, height);

                this.log(`🎨 頻譜圖繪製完成: ${numFrames}幀 x ${numBins}頻率bins`);
            }

            // 高效的FFT實現（基於Web Audio API）
            fft(signal) {
                const N = signal.length;
                const spectrum = new Array(N / 2);

                // 使用更高效的算法計算功率譜
                for (let k = 0; k < N / 2; k++) {
                    let real = 0;
                    let imag = 0;

                    // 優化的DFT計算，減少三角函數調用
                    const angleStep = -2 * Math.PI * k / N;
                    let cosAngle = 1;
                    let sinAngle = 0;
                    const cosStep = Math.cos(angleStep);
                    const sinStep = Math.sin(angleStep);

                    for (let n = 0; n < N; n++) {
                        real += signal[n] * cosAngle;
                        imag += signal[n] * sinAngle;

                        // 更新角度（避免重複計算三角函數）
                        const newCos = cosAngle * cosStep - sinAngle * sinStep;
                        const newSin = sinAngle * cosStep + cosAngle * sinStep;
                        cosAngle = newCos;
                        sinAngle = newSin;
                    }

                    // 計算功率譜密度
                    const power = (real * real + imag * imag) / (N * N);

                    // 轉換為dB，添加小的偏移避免log(0)
                    const db = power > 1e-10 ? 10 * Math.log10(power + 1e-10) : -100;
                    spectrum[k] = Math.max(-100, Math.min(20, db)); // 限制在-100到20dB範圍
                }

                return spectrum;
            }

            // 繪製頻譜圖
            drawSpectrogram(ctx, width, height) {
                if (!this.spectrogramData) {
                    this.log('❌ 無頻譜圖數據');
                    return;
                }

                const numFrames = this.spectrogramData.length;
                const numBins = this.spectrogramData[0].length;

                this.log(`🎨 開始繪製頻譜圖: ${numFrames}幀 x ${numBins}bins, canvas: ${width}x${height}`);

                // 清除canvas
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, width, height);

                // 計算當前視圖範圍
                const { startTime, endTime } = this.getVisibleTimeRange();
                const totalDuration = this.totalDuration;

                // 計算要顯示的幀範圍
                const startFrame = Math.floor((startTime / totalDuration) * numFrames);
                const endFrame = Math.min(Math.ceil((endTime / totalDuration) * numFrames), numFrames);
                const visibleFrames = Math.max(1, endFrame - startFrame);

                this.log(`📊 視圖範圍: 時間${startTime.toFixed(3)}-${endTime.toFixed(3)}s, 幀${startFrame}-${endFrame}`);

                // 創建圖像數據
                const imageData = ctx.createImageData(width, height);
                const data = imageData.data;

                // 使用固定的dB範圍以確保一致的顯示
                const minDbThreshold = -80; // 最小顯示閾值
                const maxDbThreshold = 0;   // 最大顯示閾值

                this.log(`📈 使用固定強度範圍: ${minDbThreshold}dB 到 ${maxDbThreshold}dB`);

                for (let x = 0; x < width; x++) {
                    const frameIndex = startFrame + Math.floor((x / width) * visibleFrames);
                    if (frameIndex >= 0 && frameIndex < numFrames) {
                        const spectrum = this.spectrogramData[frameIndex];

                        for (let y = 0; y < height; y++) {
                            const binIndex = Math.floor(((height - y - 1) / height) * numBins);
                            if (binIndex >= 0 && binIndex < numBins) {
                                const db = spectrum[binIndex];

                                // 使用固定範圍映射強度
                                const normalizedDb = Math.max(0, Math.min(1, (db - minDbThreshold) / (maxDbThreshold - minDbThreshold)));
                                const intensity = Math.floor(normalizedDb * 255);

                                // 使用CoolEdit風格配色方案
                                const color = this.getSpectrogramColor(intensity);

                                const pixelIndex = (y * width + x) * 4;
                                data[pixelIndex] = color.r;     // R
                                data[pixelIndex + 1] = color.g; // G
                                data[pixelIndex + 2] = color.b; // B
                                data[pixelIndex + 3] = 255;    // A
                            }
                        }
                    }
                }

                ctx.putImageData(imageData, 0, 0);
                this.log('✅ 頻譜圖繪製完成');
            }

            // 獲取頻譜圖顏色（CoolEdit風格紫紅色配色）
            getSpectrogramColor(intensity) {
                // 將0-255的強度值映射到CoolEdit風格的紫紅色方案
                const normalized = intensity / 255;

                let r, g, b;

                if (normalized < 0.2) {
                    // 深藍色到紫色
                    r = Math.floor(normalized * 5 * 100);
                    g = 0;
                    b = Math.floor(50 + normalized * 5 * 100);
                } else if (normalized < 0.4) {
                    // 紫色到洋紅色
                    const t = (normalized - 0.2) * 5;
                    r = Math.floor(100 + t * 155);
                    g = 0;
                    b = Math.floor(150 - t * 50);
                } else if (normalized < 0.6) {
                    // 洋紅色到紅色
                    const t = (normalized - 0.4) * 5;
                    r = 255;
                    g = Math.floor(t * 100);
                    b = Math.floor(100 - t * 100);
                } else if (normalized < 0.8) {
                    // 紅色到橙色
                    const t = (normalized - 0.6) * 5;
                    r = 255;
                    g = Math.floor(100 + t * 155);
                    b = 0;
                } else {
                    // 橙色到黃色
                    const t = (normalized - 0.8) * 5;
                    r = 255;
                    g = 255;
                    b = Math.floor(t * 200);
                }

                return { r, g, b };
            }

            // 更新頻譜圖顯示
            updateSpectrogramDisplay() {
                const canvas = document.getElementById('spectrogramCanvas');
                if (!canvas) {
                    this.log('❌ 找不到頻譜圖canvas');
                    return;
                }

                const ctx = canvas.getContext('2d');

                // 設置canvas尺寸
                const container = canvas.parentElement;
                if (!container) {
                    this.log('❌ 找不到canvas容器');
                    return;
                }

                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;

                this.log(`🖼️ 更新頻譜圖顯示: ${canvas.width}x${canvas.height}`);

                if (this.spectrogramData) {
                    this.drawSpectrogram(ctx, canvas.width, canvas.height);
                } else {
                    // 如果沒有頻譜圖數據，生成它
                    this.log('🔄 頻譜圖數據不存在，重新生成...');
                    this.generateSpectrogram();
                }
            }

            // 通用的顯示更新方法
            updateDisplay() {
                if (this.displayMode === 'waveform') {
                    this.drawWaveform();
                } else if (this.displayMode === 'spectrogram') {
                    this.updateSpectrogramDisplay();
                }
            }

            // 更新頻率軸顯示（頻譜圖模式）
            updateFrequencyAxis() {
                const amplitudeAxis = document.getElementById('amplitudeAxis');
                if (!amplitudeAxis) return;

                // 設置頻率軸的背景色，並禁用拖拽
                amplitudeAxis.style.backgroundColor = '#7B7B7B';
                amplitudeAxis.style.cursor = 'default'; // 禁用拖拽游標

                // 確保縱軸高度與波形容器一致
                const waveformContainer = document.querySelector('.waveform-container');
                if (waveformContainer) {
                    amplitudeAxis.style.height = waveformContainer.offsetHeight + 'px';
                }

                amplitudeAxis.innerHTML = '';
                const height = amplitudeAxis.offsetHeight || 400;

                // 計算頻率範圍（0 到 Nyquist頻率）
                const maxFreq = this.sampleRate / 2;

                // 使用線性刻度生成頻率值
                let freqValues = [];
                const numTicks = 10; // 固定顯示10個刻度
                const freqStep = maxFreq / (numTicks - 1);

                for (let i = 0; i < numTicks; i++) {
                    const freq = i * freqStep;
                    freqValues.push(Math.round(freq));
                }

                // 清除現有內容
                amplitudeAxis.innerHTML = '';

                // 清除舊的網格線
                if (waveformContainer) {
                    waveformContainer.querySelectorAll('.amplitude-grid-line').forEach(line => line.remove());
                }

                this.log(`📊 頻率軸更新: 最大頻率${maxFreq}Hz, 刻度${freqValues.length}個`);

                // 為每個頻率值創建刻度
                freqValues.forEach((freq, index) => {
                    // 計算Y位置（頻率軸是從下到上，0Hz在底部）
                    const normalizedFreq = freq / maxFreq;
                    const y = height - (normalizedFreq * height);

                    if (y >= 0 && y <= height) {
                        // 主要刻度線
                        const majorTick = document.createElement('div');
                        majorTick.className = 'amplitude-tick major';
                        majorTick.style.position = 'absolute';
                        majorTick.style.top = (y - 1) + 'px';
                        majorTick.style.left = '0px';
                        majorTick.style.height = '2px';
                        majorTick.style.width = '8px';
                        majorTick.style.backgroundColor = '#fff';
                        amplitudeAxis.appendChild(majorTick);

                        // 頻率標籤
                        const label = document.createElement('div');
                        label.className = 'amplitude-label';
                        label.style.position = 'absolute';
                        label.style.top = (y - 8) + 'px';
                        label.style.left = '12px';
                        label.style.fontSize = '11px';
                        label.style.color = '#ccc';
                        label.style.whiteSpace = 'nowrap';
                        label.style.backgroundColor = 'rgba(0,0,0,0.7)';
                        label.style.padding = '1px 3px';
                        label.style.borderRadius = '2px';

                        // 格式化頻率顯示
                        if (freq >= 1000) {
                            label.textContent = (freq / 1000).toFixed(freq % 1000 === 0 ? 0 : 1) + 'k';
                        } else {
                            label.textContent = freq.toString();
                        }

                        amplitudeAxis.appendChild(label);

                        // 添加網格線到波形容器
                        if (waveformContainer) {
                            const gridLine = document.createElement('div');
                            gridLine.className = 'amplitude-grid-line';
                            gridLine.style.position = 'absolute';
                            gridLine.style.top = (y - 1) + 'px';
                            gridLine.style.left = '0px';
                            gridLine.style.right = '0px';
                            gridLine.style.height = '1px';
                            gridLine.style.backgroundColor = 'rgba(255,255,255,0.1)';
                            gridLine.style.pointerEvents = 'none';
                            gridLine.style.zIndex = '1';
                            waveformContainer.appendChild(gridLine);
                        }
                    }
                });
            }

            // 更新頻譜圖tooltip顯示
            updateSpectrogramTooltip(mouseX, mouseY) {
                const tooltip = document.getElementById('spectrogramTooltip');
                const spectrogramValue = document.getElementById('spectrogramValue');

                if (!this.spectrogramData || !this.audioData) {
                    tooltip.style.display = 'none';
                    return;
                }

                // 計算鼠標位置對應的時間和頻率
                const { startTime, endTime } = this.getVisibleTimeRange();
                const timeAtMouse = startTime + (mouseX / this.pixelsPerSecond);

                const canvas = document.getElementById('spectrogramCanvas');
                const height = canvas.height;
                const numFrames = this.spectrogramData.length;
                const numBins = this.spectrogramData[0].length;

                // 計算幀索引
                const frameIndex = Math.floor((timeAtMouse / this.totalDuration) * numFrames);

                // 計算頻率bin索引（Y軸翻轉）
                const binIndex = Math.floor(((height - mouseY) / height) * numBins);

                if (frameIndex >= 0 && frameIndex < numFrames && binIndex >= 0 && binIndex < numBins) {
                    const db = this.spectrogramData[frameIndex][binIndex];
                    const frequency = (binIndex / numBins) * (this.sampleRate / 2);

                    // 格式化頻率顯示
                    let freqText;
                    if (frequency >= 1000) {
                        freqText = (frequency / 1000).toFixed(1) + 'kHz';
                    } else {
                        freqText = frequency.toFixed(0) + 'Hz';
                    }

                    spectrogramValue.textContent = `時間: ${timeAtMouse.toFixed(3)}s, 頻率: ${freqText}, 強度: ${db.toFixed(1)}dB`;

                    // 定位tooltip
                    tooltip.style.left = (mouseX + 10) + 'px';
                    tooltip.style.top = (mouseY - 25) + 'px';
                    tooltip.style.display = 'block';
                } else {
                    tooltip.style.display = 'none';
                }
            }

            // 工具欄功能
             connectRTP() {
                this.log('🔌 嘗試連接RTP服務器...');
                // RTP連接邏輯
                setTimeout(() => {
                    const status = document.getElementById('connectionStatus');
                    const statusText = document.getElementById('statusText');
                    status.classList.toggle('connected');
                    statusText.textContent = status.classList.contains('connected') ? 
                        'RTP已連接' : 'WAV文件分析器';
                    this.log(status.classList.contains('connected') ? 
                        '✅ RTP連接成功' : '❌ RTP連接斷開');
                }, 1000);
            }
            
            toggleRecording() {
                const btn = document.getElementById('recordBtn');
                if (btn.classList.contains('active')) {
                    btn.classList.remove('active');
                    this.log('⏹️ 停止錄製');
                } else {
                    btn.classList.add('active');
                    this.log('🎙️ 開始錄製');
                }
            }
            
            // 動畫循環
            startAnimationLoop() {
                const animate = () => {
                    this.drawWaveform();
                    this.drawSpectrum();
                    this.updateLevelMeters();


                    this.updatePlayhead();
                    requestAnimationFrame(animate);
                };

                animate();
            }
            
            log(message) {
                const log = document.getElementById('log');
                const time = new Date().toLocaleTimeString();
                log.innerHTML += `<br>[${time}] ${message}`;
                log.scrollTop = log.scrollHeight;
            }
        }
        
        // 全局實例
        let analyzer = null;
        
        // 頁面加載完成後初始化
        document.addEventListener('DOMContentLoaded', () => {
            analyzer = new ProfessionalWavAnalyzer();
        });
    </script>
</body>
</html>