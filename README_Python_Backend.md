# 🎵 Audio Visualization with Python Backend

这个项目现在支持两种spectrogram计算方式：
1. **Python后端** (推荐) - 使用librosa提供高质量计算
2. **JavaScript前端** - 原有的浏览器内计算

## 🚀 快速开始

### 1. 启动Python后端服务器

**Windows:**
```bash
start_server.bat
```

**Linux/Mac:**
```bash
chmod +x start_server.sh
./start_server.sh
```

**手动启动:**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动服务器
python spectrogram_server.py
```

### 2. 打开前端界面

在浏览器中打开 `audio_visual.html`

## 🎯 功能特点

### Python后端优势
- ✅ **高质量计算**: 使用librosa专业音频库
- ✅ **Adobe Audition风格**: 专业级参数设置
- ✅ **更快速度**: 优化的FFT算法
- ✅ **更准确**: 与scipy.signal.spectrogram一致
- ✅ **自动回退**: 如果Python服务不可用，自动使用JavaScript

### 参数配置
```python
AUDITION_OPTIMAL_PARAMS = {
    'n_fft': 2048,           # FFT 大小，平衡頻率和時間解析度
    'hop_length': 512,       # 75% 重疊
    'window': 'hamming',     # Adobe 推薦的窗函數
    'freq_scale': 'linear',  # 線性頻率軸
    'dynamic_range': 60,     # dB 動態範圍
    'color_map': 'viridis'   # 現代替代品
}
```

## 🔧 API端点

### GET /health
健康检查端点
```bash
curl http://localhost:5000/health
```

### POST /spectrogram
计算spectrogram (JSON数据)
```bash
curl -X POST http://localhost:5000/spectrogram \
  -H "Content-Type: application/json" \
  -d '{
    "audio_data": [0.1, 0.2, ...],
    "sample_rate": 44100,
    "params": {...}
  }'
```

### POST /spectrogram_from_file
计算spectrogram (文件上传)
```bash
curl -X POST http://localhost:5000/spectrogram_from_file \
  -F "audio_file=@test.wav" \
  -F "params={}"
```

## 🎨 前端控制

在HTML中可以控制使用哪种计算方式：

```javascript
// 使用Python后端 (推荐)
this.usePythonBackend = true;

// 使用JavaScript前端
this.usePythonBackend = false;
```

## 🐛 故障排除

### Python服务器无法启动
1. 确保Python 3.7+已安装
2. 检查端口5000是否被占用
3. 查看控制台错误信息

### 前端无法连接后端
1. 确认Python服务器正在运行
2. 检查CORS设置
3. 查看浏览器控制台错误

### 自动回退机制
如果Python后端不可用，系统会自动：
1. 显示错误信息
2. 设置 `usePythonBackend = false`
3. 使用JavaScript计算
4. 继续正常工作

## 📊 性能对比

| 特性 | Python后端 | JavaScript前端 |
|------|------------|----------------|
| 计算速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 精度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 兼容性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 设置复杂度 | ⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🔮 未来计划

- [ ] 支持更多音频格式
- [ ] 实时音频流处理
- [ ] 批量文件处理
- [ ] 更多可视化选项
- [ ] WebSocket实时通信
