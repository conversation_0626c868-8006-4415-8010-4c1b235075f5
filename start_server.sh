#!/bin/bash

echo "🚀 启动Spectrogram Python后端服务器..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📚 安装Python依赖..."
pip install -r requirements.txt

# 启动服务器
echo "🌐 启动Flask服务器..."
echo "服务器将在 http://localhost:5000 运行"
echo "按 Ctrl+C 停止服务器"
echo
python spectrogram_server.py
