--- audio_visual.html.orig
+++ audio_visual.html
@@ -1090,6 +1090,9 @@
                 // 菜單系統狀態
                 this.activeMenu = null;
                 this.isMenuOpen = false;
+                
+                // 拖拽覆盖层相关变量
+                this.dragOverlay = null;
 
             
                 // 右键菜单和剪贴板
@@ -1151,47 +1154,113 @@
             }
             setupDropZone() {
-                const container = document.querySelector('.main-area');
+                // 改为使用整个主区域作为拖拽区域
+                const container = document.querySelector('.main-area');
                 const dropZone = document.getElementById('dropZone');
+                const waveformContainer = document.querySelector('.waveform-container');
                 
-                // 防止默認拖拽行為
+                // 防止默認拖拽行為 - 应用到整个主区域
                 ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
-                    dropZone.addEventListener(eventName, (e) => {
+                    container.addEventListener(eventName, (e) => {
                         e.preventDefault();
                         e.stopPropagation();
                     });
                     
                     document.addEventListener(eventName, (e) => {
                         e.preventDefault();
                         e.stopPropagation();
                     });
                 });
                 
-                // 拖拽效果
-                dropZone.addEventListener('dragenter', () => {
-                    dropZone.classList.add('dragover');
+                // 拖拽效果 - 在整个主区域
+                container.addEventListener('dragenter', (e) => {
+                    // 显示拖拽提示
+                    this.showDragOverlay();
                     this.log('🔵 檢測到文件拖拽進入');
                 });
                 
-                dropZone.addEventListener('dragover', () => {
-                    dropZone.classList.add('dragover');
+                container.addEventListener('dragover', (e) => {
+                    this.showDragOverlay();
                 });
                 
-                dropZone.addEventListener('dragleave', (e) => {
-                    if (!dropZone.contains(e.relatedTarget)) {
-                        dropZone.classList.remove('dragover');
+                container.addEventListener('dragleave', (e) => {
+                    // 检查是否真的离开了主区域
+                    if (!container.contains(e.relatedTarget)) {
+                        this.hideDragOverlay();
                         this.log('🔴 文件拖拽離開');
                     }
                 });
                 
-                // 文件釋放
-                dropZone.addEventListener('drop', (e) => {
-                    dropZone.classList.remove('dragover');
+                // 文件釋放 - 在整个主区域
+                container.addEventListener('drop', (e) => {
+                    this.hideDragOverlay();
                     this.log('📥 文件釋放事件觸發');
                     
                     const files = Array.from(e.dataTransfer.files);
                     this.log(`📂 檢測到 ${files.length} 個文件`);
                     
                     if (files.length > 0) {
                         const file = files[0];
                         this.log(`文件名: ${file.name}, 大小: ${file.size} 字節, 類型: ${file.type}`);
                         this.processFile(file);
                     }
                 });
                 
-                // 點擊選擇文件
-                dropZone.addEventListener('click', () => {
+                // 点击选择文件 - 可以点击任何地方
+                const clickToSelect = () => {
                     const input = document.createElement('input');
                     input.type = 'file';
                     input.accept = '.wav,audio/wav,audio/x-wav';
                     input.onchange = (e) => {
                         if (e.target.files.length > 0) {
                             this.log(`通過點擊選擇了文件: ${e.target.files[0].name}`);
                             this.processFile(e.target.files[0]);
                         }
                     };
                     input.click();
-                });
+                };
+                
+                // 初始状态的点击事件
+                dropZone.addEventListener('click', clickToSelect);
+                
+                // 如果已经加载了文件，在波形区域双击也能重新选择
+                waveformContainer.addEventListener('dblclick', (e) => {
+                    // 只有在按住Ctrl时才触发文件选择，避免与其他双击功能冲突
+                    if (e.ctrlKey || e.metaKey) {
+                        e.stopPropagation();
+                        clickToSelect();
+                        this.log('🔄 Ctrl+雙擊重新選擇文件');
+                    }
+                });
             }
+            
+            // 显示拖拽覆盖层
+            showDragOverlay() {
+                if (!this.dragOverlay) {
+                    this.createDragOverlay();
+                }
+                this.dragOverlay.style.display = 'flex';
+            }
+            
+            // 隐藏拖拽覆盖层
+            hideDragOverlay() {
+                if (this.dragOverlay) {
+                    this.dragOverlay.style.display = 'none';
+                }
+            }
+            
+            // 创建拖拽覆盖层
+            createDragOverlay() {
+                this.dragOverlay = document.createElement('div');
+                this.dragOverlay.style.cssText = `
+                    position: absolute;
+                    top: 0;
+                    left: 0;
+                    right: 0;
+                    bottom: 0;
+                    background: rgba(0, 123, 204, 0.1);
+                    border: 3px dashed #007acc;
+                    display: none;
+                    flex-direction: column;
+                    align-items: center;
+                    justify-content: center;
+                    z-index: 1000;
+                    pointer-events: none;
+                    backdrop-filter: blur(2px);
+                `;
+                
+                const icon = document.createElement('div');
+                icon.style.cssText = `
+                    font-size: 48px;
+                    margin-bottom: 20px;
+                    color: #007acc;
+                `;
+                icon.textContent = '📁';
+                
+                const text = document.createElement('div');
+                text.style.cssText = `
+                    font-size: 24px;
+                    color: #007acc;
+                    font-weight: bold;
+                    text-align: center;
+                    margin-bottom: 10px;
+                `;
+                text.textContent = this.audioData ? '拖拽新的WAV文件以重新載入' : '釋放以載入WAV文件';
+                
+                const subtext = document.createElement('div');
+                subtext.style.cssText = `
+                    font-size: 16px;
+                    color: #005c99;
+                    text-align: center;
+                `;
+                subtext.textContent = this.audioData ? '支持替換當前文件' : '支持WAV格式文件';
+                
+                this.dragOverlay.appendChild(icon);
+                this.dragOverlay.appendChild(text);
+                this.dragOverlay.appendChild(subtext);
+                
+                const mainArea = document.querySelector('.main-area');
+                mainArea.appendChild(this.dragOverlay);
+            }
             
             async processFile(file) {
                 try {
                     this.log(`🔄 開始處理文件: ${file.name}`);
                     
+                    // 如果已经有文件加载，显示重新加载信息
+                    if (this.audioData) {
+                        this.log('🔄 重新載入新文件，清除舊數據...');
+                        this.clearPreviousData();
+                    }
+                    
                     // 檢查文件類型
                     const isWav = file.type.includes('wav') || file.name.toLowerCase().endsWith('.wav');
                     if (!isWav) {
                         throw new Error('不是WAV文件格式');
                     }
                     
                     // 讀取文件
                     const arrayBuffer = await file.arrayBuffer();
                     this.log(`文件讀取完成，大小: ${arrayBuffer.byteLength} 字節`);
                     
                     // 初始化音頻上下文
                     try {
                         await this.initAudioContext();
                     } catch (error) {
                         this.log('⚠️ 音頻播放功能不可用，但可以查看波形');
                     }
                     // 解析WAV文件
                     const audioData = this.parseWav(arrayBuffer);
                     this.log(`WAV解析成功，採樣率: ${audioData.sampleRate}Hz, 樣本數: ${audioData.samples.length}`);
                     
                     // 保存數據
                     this.audioData = audioData.samples;
                     this.sampleRate = audioData.sampleRate;
                     this.totalDuration = audioData.samples.length / audioData.sampleRate;
-                    
 
-                      
                     // 創建AudioBuffer用於播放
                     if (this.audioContext) {
                         try {
                             this.audioBuffer = this.audioContext.createBuffer(1, audioData.samples.length, audioData.sampleRate);
                             const channelData = this.audioBuffer.getChannelData(0);
                             for (let i = 0; i < audioData.samples.length; i++) {
                                 channelData[i] = audioData.samples[i];
                             }
                             this.enablePlaybackControls();
                         } catch (error) {
                             this.log('⚠️ 音頻緩衝區創建失敗: ' + error.message);
                         }
                     }
+                    
                     // 更新顯示緩衝區
                     const displayLength = Math.min(8192, this.audioData.length);
                     this.displayBuffer = new Float32Array(displayLength);
                     for (let i = 0; i < displayLength; i++) {
                         this.displayBuffer[i] = this.audioData[i];
                     }
                     
                     // 計算頻譜
                     this.computeSpectrum();
                     
                     // 顯示文件信息
                     this.showFileInfo(file, audioData);
                     
-                    // 隱藏拖拽區域
+                    // 隱藏拖拽區域，显示波形区域
                     const dropZone = document.getElementById('dropZone');
                     dropZone.classList.add('hidden');
                     
+                    // 重置选择和播放状态
+                    this.resetPlaybackState();
+                    this.resetSelection();
+                    
                     // 強制重新調整canvas尺寸
                     this.updateLayout();
                     // 自動調整縮放
                     this.autoFitWaveform();
                     
-                    this.log('✅ 文件處理完成，拖拽區域已隱藏');
+                    this.log('✅ 文件處理完成，可以開始分析');
                     
                 } catch (error) {
                     this.log(`❌ 處理文件時出錯: ${error.message}`);
                     console.error('處理文件錯誤:', error);
                 }
             }
+            
+            // 清除之前的数据
+            clearPreviousData() {
+                // 停止当前播放
+                if (this.isPlaying) {
+                    this.stopAudio();
+                }
+                
+                // 清除音频数据
+                this.audioData = null;
+                this.audioBuffer = null;
+                this.displayBuffer = null;
+                this.spectrumData = new Uint8Array(1024);
+                
+                // 重置音频参数
+                this.totalDuration = 0;
+                this.currentPlayTime = 0;
+                this.pauseTime = 0;
+                
+                // 清除剪贴板
+                this.clipboard = null;
+                this.updatePasteStatus();
+            }
+            
+            // 重置播放状态
+            resetPlaybackState() {
+                this.isPlaying = false;
+                this.currentPlayTime = 0;
+                this.pauseTime = 0;
+                this.startTime = 0;
+                
+                // 重置播放按钮状态
+                document.getElementById('playBtn').textContent = '播放';
+                document.getElementById('playBtn').disabled = true;
+                document.getElementById('stopBtn').disabled = true;
+                document.getElementById('timeDisplay').textContent = '0:00.000';
+            }
+            
+            // 重置选择状态
+            resetSelection() {
+                this.selectionStart = 0;
+                this.selectionEnd = 0;
+                
+                // 隐藏选择框
+                const selectionBox = document.getElementById('selectionBox');
+                selectionBox.style.display = 'none';
+                
+                // 重置选择信息显示
+                document.getElementById('selBegin').textContent = '0:00.000';
+                document.getElementById('selEnd').textContent = '0:00.000';
+                document.getElementById('selLength').textContent = '0:00.000';
+            }
             
             parseWav(arrayBuffer) {
@@ -1642,8 +1711,8 @@
                 });
                 
-                // 雙擊全選
+                // 雙擊功能：默认全选，Ctrl+双击选择新文件
                 container.addEventListener('dblclick', () => {
                     if (!this.audioData) return;
-                    this.selectAll();
+                    
+                    if (e.ctrlKey || e.metaKey) {
+                        // Ctrl+双击：选择新文件
+                        e.stopPropagation();
+                        this.selectNewFile();
+                    } else {
+                        // 普通双击：全选
+                        this.selectAll();
+                    }
                 });
             }
+            
+            // 选择新文件的方法
+            selectNewFile() {
+                const input = document.createElement('input');
+                input.type = 'file';
+                input.accept = '.wav,audio/wav,audio/x-wav';
+                input.onchange = (e) => {
+                    if (e.target.files.length > 0) {
+                        this.log(`🔄 Ctrl+雙擊選擇新文件: ${e.target.files[0].name}`);
+                        this.processFile(e.target.files[0]);
+                    }
+                };
+                input.click();
+            }
 
             
            // 保存选择状态
@@ -2345,8 +2445,8 @@
             executeMenuAction(action) {
                 switch(action) {
                     case 'open':
-                        // 觸發檔案選擇
-                        document.getElementById('dropZone').click();
+                        // 觸發檔案選擇
+                        this.selectNewFile();
                         break;
                     case 'select-all':
                         if (this.audioData) this.selectAll();
                         break;
                     case 'zoom-in':
                         this.zoomIn();
                         break;
                     case 'zoom-out':
                         this.zoomOut();
                         break;
                     case 'zoom-fit':
                         if (this.audioData) this.autoFitWaveform();
                         break;
                     case 'toggle-spectrum':
                         document.getElementById('showBottomPanel').click();
                         break;
                     case 'about':
-                        alert('專業WAV文件分析器 v2.0\n\n具備高精度波形顯示和頻譜分析功能');
+                        alert('專業WAV文件分析器 v2.0\n\n具備高精度波形顯示和頻譜分析功能\n\n使用說明：\n- 拖拽WAV文件到任意區域載入\n- Ctrl+雙擊可重新選擇文件\n- 支持無限重載入功能');
                         break;
                     default:
                         this.log(`選單操作: ${action}`);
                         break;
                 }
             }