# -*- coding: utf-8 -*-
"""
Spectrogram Server - Python后端服务
提供高质量的spectrogram计算，供JavaScript前端调用
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import librosa
import numpy as np
import base64
import io
import tempfile
import os
import json

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# Adobe Audition 風格的最佳參數
AUDITION_OPTIMAL_PARAMS = {
    'n_fft': 2048,           # FFT 大小，平衡頻率和時間解析度
    'hop_length': 512,       # 75% 重疊 (2048 * 0.75 = 1536, 2048-1536=512)
    'window': 'hamming',     # Adobe 推薦的窗函數
    'freq_scale': 'linear',  # 線性頻率軸
    'dynamic_range': 60,     # dB 動態範圍
    'color_map': 'viridis'   # 現代替代品，原版使用類似的色譜
}

def create_cooledit_style_spectrogram(audio_data, sr=44100, params=None):
    """
    重現 Cool Edit Pro 經典的 spectrogram 風格
    """
    if params is None:
        params = AUDITION_OPTIMAL_PARAMS
    
    # Cool Edit Pro 典型參數
    n_fft = params['n_fft']
    hop_length = params['hop_length']
    window = params['window']
    dynamic_range = params['dynamic_range']
    
    print(f"🔧 計算Spectrogram: n_fft={n_fft}, hop_length={hop_length}, window={window}")
    
    # 計算 STFT
    stft = librosa.stft(
        audio_data, 
        n_fft=n_fft, 
        hop_length=hop_length, 
        window=window
    )
    
    # 轉換為 magnitude spectrogram
    magnitude = np.abs(stft)
    magnitude_db = librosa.amplitude_to_db(magnitude, ref=np.max)
    
    # 限制動態範圍為60dB
    max_db = np.max(magnitude_db)
    min_db_threshold = max_db - dynamic_range
    magnitude_db = np.clip(magnitude_db, min_db_threshold, max_db)
    
    # 計算時間和頻率軸
    times = librosa.frames_to_time(np.arange(magnitude_db.shape[1]), sr=sr, hop_length=hop_length)
    frequencies = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
    
    print(f"📊 Spectrogram形狀: {magnitude_db.shape} (頻率bins x 時間幀)")
    print(f"📈 dB範圍: {np.min(magnitude_db):.1f} 到 {np.max(magnitude_db):.1f}")
    print(f"⏱️ 時間範圍: 0 到 {times[-1]:.2f}秒")
    print(f"🎵 頻率範圍: 0 到 {frequencies[-1]:.0f}Hz")
    
    return {
        'magnitude_db': magnitude_db.tolist(),  # 轉換為列表以便JSON序列化
        'times': times.tolist(),
        'frequencies': frequencies.tolist(),
        'sample_rate': sr,
        'params': params,
        'shape': magnitude_db.shape,
        'db_range': [float(np.min(magnitude_db)), float(np.max(magnitude_db))]
    }

@app.route('/health', methods=['GET'])
def health_check():
    """健康檢查端點"""
    return jsonify({'status': 'ok', 'message': 'Spectrogram server is running'})

@app.route('/spectrogram', methods=['POST'])
def calculate_spectrogram():
    """計算spectrogram的主要端點"""
    try:
        # 檢查請求數據
        if 'audio_data' not in request.json:
            return jsonify({'error': 'Missing audio_data'}), 400
        
        # 獲取音頻數據
        audio_data = np.array(request.json['audio_data'], dtype=np.float32)
        sample_rate = request.json.get('sample_rate', 44100)
        
        # 獲取自定義參數（如果有）
        custom_params = request.json.get('params', {})
        params = AUDITION_OPTIMAL_PARAMS.copy()
        params.update(custom_params)
        
        print(f"🎵 收到音頻數據: {len(audio_data)} 樣本, {sample_rate}Hz")
        
        # 計算spectrogram
        result = create_cooledit_style_spectrogram(audio_data, sample_rate, params)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        print(f"❌ 錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/spectrogram_from_file', methods=['POST'])
def calculate_spectrogram_from_file():
    """從上傳的文件計算spectrogram"""
    try:
        # 檢查是否有文件上傳
        if 'audio_file' not in request.files:
            return jsonify({'error': 'No audio file uploaded'}), 400
        
        file = request.files['audio_file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # 保存臨時文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            file.save(temp_file.name)
            temp_filename = temp_file.name
        
        try:
            # 載入音頻文件
            audio_data, sample_rate = librosa.load(temp_filename, sr=44100)
            
            # 獲取自定義參數
            custom_params = {}
            if 'params' in request.form:
                custom_params = json.loads(request.form['params'])
            
            params = AUDITION_OPTIMAL_PARAMS.copy()
            params.update(custom_params)
            
            print(f"🎵 載入音頻文件: {len(audio_data)} 樣本, {sample_rate}Hz")
            
            # 計算spectrogram
            result = create_cooledit_style_spectrogram(audio_data, sample_rate, params)
            
            return jsonify({
                'success': True,
                'data': result
            })
            
        finally:
            # 清理臨時文件
            os.unlink(temp_filename)
        
    except Exception as e:
        print(f"❌ 錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 啟動Spectrogram服務器...")
    print("📡 端點:")
    print("  GET  /health - 健康檢查")
    print("  POST /spectrogram - 計算spectrogram (JSON數據)")
    print("  POST /spectrogram_from_file - 計算spectrogram (文件上傳)")
    print("🌐 服務器地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
