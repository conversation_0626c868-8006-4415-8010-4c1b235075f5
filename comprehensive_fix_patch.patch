--- a/audio_visual.html
+++ b/audio_visual.html
@@ -200,6 +200,12 @@
         .resize-handle {
            height: 5px;
             background: #555;
             cursor: ns-resize;
+            border-top: 1px solid #666;
+            border-bottom: 1px solid #666;
+            user-select: none;
+            position: relative;
+            z-index: 10;
+            flex-shrink: 0;
         }
+        
+        .resize-handle:hover {
+            background: #777;
+        }
+        
+        .resize-handle::before {
+            content: '⋯';
+            position: absolute;
+            left: 50%;
+            top: 50%;
+            transform: translate(-50%, -50%);
+            color: #aaa;
+            font-size: 12px;
+            letter-spacing: 2px;
+        }
+        
         .bottom-panel.hidden {
          display: none;
          }
         .test-container {
             display: flex;
             flex-direction: column;
-            height: calc(100vh - 90px);
+            height: calc(100vh - 120px); /* 为菜单栏留出空间 */
             background: #1a1a1a;
             border: 1px solid #333;
+            overflow: hidden;
         }
         
         .main-area {
-            flex: 0 0 40vh;
-            max-height: 40vh;
+            flex: 0 0 45vh;
+            min-height: 200px;
+            max-height: 70vh;
             position: relative;
             display: flex;
             flex-direction: column;
+            overflow: hidden;
         }
         
+        /* 添加高度调整条样式 */
+        .height-adjuster {
+            height: 8px;
+            background: linear-gradient(to bottom, #4a4a4a, #3a3a3a);
+            border-top: 1px solid #555;
+            border-bottom: 1px solid #555;
+            cursor: ns-resize;
+            position: relative;
+            user-select: none;
+            flex-shrink: 0;
+        }
+        
+        .height-adjuster:hover {
+            background: linear-gradient(to bottom, #5a5a5a, #4a4a4a);
+        }
+        
+        .height-adjuster::before {
+            content: '═══';
+            position: absolute;
+            left: 50%;
+            top: 50%;
+            transform: translate(-50%, -50%);
+            color: #888;
+            font-size: 10px;
+            letter-spacing: 1px;
+        }
+        
         /* 修改拖拽樣式 */
         .drop-zone {
             position: absolute;
@@ -320,6 +370,35 @@
             right: 5px;
             transform: translateY(-50%);
             white-space: nowrap;
+            font-family: 'Courier New', monospace;
+        }
+        
+        /* 纵轴刻度线样式 */
+        .amplitude-tick {
+            position: absolute;
+            right: 0;
+            width: 8px;
+            background: #999;
+            border-right: 1px solid #aaa;
+        }
+        
+        .amplitude-tick.major {
+            width: 12px;
+            background: #bbb;
+        }
+        
+        .amplitude-tick.minor {
+            width: 5px;
+            background: #777;
+        }
+        
+        .amplitude-grid-line {
+            position: absolute;
+            left: 0;
+            right: 0;
+            height: 1px;
+            background: rgba(255, 255, 255, 0.1);
+            pointer-events: none;
         }
         
         .waveform-canvas {
@@ -400,15 +479,21 @@
         /* 時間軸 */
         .timeline {
             background: #7B7B7B;
             height: 25px;
+            min-height: 25px;
+            flex-shrink: 0;
             
             position: relative;
             font-size: 10px;
             color: #f5f1f1;
             margin-left: 0;
             margin-right: 80px;
+            border-bottom: 1px solid #555;
         }

         .timeline-marker {
             position: absolute;
             top: 0;
             bottom: 0;
             border-left: 1px solid #555;
             padding-left: 3px;
             padding-top: 2px;
+            font-family: 'Courier New', monospace;
         }

         .timeline-marker.major {
@@ -440,13 +525,17 @@
         /* 底部面板 */
         .bottom-panel {
             background: #2a2a2a;
-            flex: 1;
+            flex: 1 1 auto;
             border-top: 1px solid #555;
-            min-height: 200px;
+            min-height: 150px;
             display: flex;
+            overflow: hidden;
         }
         
+        .log {
+            background: #1a1a1a;
+            border-top: 1px solid #555;
+            padding: 8px 10px;
+            max-height: 80px;
+            min-height: 60px;
+            overflow-y: auto;
+            font-family: monospace;
+            font-size: 10px;
+            color: #888;
+            flex-shrink: 0;
+        }
+        
         .spectrum-panel {
             flex: 2;
             border-right: 1px solid #555;
             position: relative;
+            min-width: 0;
         }

         .spectrum-canvas {
             width: 90%;
             height: 90%;
             background: #000;
         }

         .frequency-labels {
             position: absolute;
             left: 0;
             top: 0;
             bottom: 0;
             width: 40px;
             background: #2a2a2a;
             border-right: 1px solid #555;
             font-size: 9px;
             color: #aaa;
             display: flex;
             flex-direction: column;
             justify-content: space-between;
             padding: 5px 2px;
         }

         .meters-panel {
-            flex: 1;
+            flex: 0 0 200px;
             padding: 10px;
             display: flex;
             flex-direction: column;
             gap: 10px;
+            min-width: 0;
         }

         .meter-group {
             background: #333;
             border: 1px solid #555;
             border-radius: 3px;
             padding: 8px;
         }

         .meter-title {
             font-size: 10px;
             color: #aaa;
             margin-bottom: 5px;
             text-transform: uppercase;
         }

         .level-meter {
             height: 20px;
             background: #1a1a1a;
             border: 1px solid #555;
             position: relative;
             overflow: hidden;
         }

         .level-bar {
             height: 100%;
             background: linear-gradient(to right, #00ff00 0%, #ffff00 70%, #ff0000 100%);
             width: 0%;
             transition: width 0.1s ease;
         }

         .level-text {
             position: absolute;
             right: 5px;
             top: 50%;
             transform: translateY(-50%);
             font-size: 10px;
             color: #fff;
             text-shadow: 1px 1px 1px #000;
         }
         
         .selection-info {
             background: #333;
             border: 1px solid #555;
             padding: 5px 8px;
             font-size: 10px;
             border-radius: 2px;
         }

         .selection-info span {
             margin-right: 15px;
         }
         
-        .log {
-            background: #1a1a1a;
-            border-top: 1px solid #555;
-            padding: 10px;
-            max-height: 100px;
-            overflow-y: auto;
-            font-family: monospace;
-            font-size: 10px;
-            color: #888;
-        }

         @media (max-width: 1024px) {
             .test-container {
-                height: calc(100vh - 100px);
+                height: calc(100vh - 130px);
             }
             .main-area {
-               flex: 0 0 30vh;
-               max-height: 30vh;
+               flex: 0 0 35vh;
+               max-height: 50vh;
             }
             .bottom-panel {
                 flex-direction: column;
                 flex: 1;
-                min-height: 150px;
+                min-height: 120px;
             }
             
             .spectrum-panel {
                 border-right: none;
                 border-bottom: 1px solid #555;
                 flex: 1;
             }
             
             .meters-panel {
-                flex: 0 0 100px;
+                flex: 0 0 80px;
             }

             .drop-zone {
                 width: 300px;
                 height: 150px;
                 font-size: 16px;
             }
+            
+            .log {
+                max-height: 60px;
+                min-height: 40px;
+            }
             
         }
@@ -632,6 +721,13 @@
                 // 縮放步進設定
                 this.timeZoomStep = 10;  // 時間軸縮放步進值
                 this.timeZoomStepLarge = 50; // 高縮放級別時的步進值
+                
+                // 自动适配缩放相关
+                this.autoFitZoomIndex = 3; // 记录自动适配时的缩放级别
+                this.hasAutoFitted = false; // 标记是否已经执行过自动适配
+                
+                // 布局调整
+                this.isResizingHeight = false;
                 
                 // 菜单系统状态
                 this.activeMenu = null;
@@ -650,6 +746,7 @@
                 this.setupTimeInfoInteraction();
                 this.setupAmplitudeAxisInteraction();
                 this.setupMenuSystem();
+                this.setupHeightAdjuster();
                 this.updateAmplitudeAxis();
                 this.setupBottomPanelToggle();
                 this.startAnimationLoop();
@@ -704,11 +801,19 @@
             zoomOut() {
                 if (this.currentZoomIndex > 0) {
+                    // 检查是否会超出自动适配的最小级别
+                    if (this.hasAutoFitted && this.currentZoomIndex - 1 < this.autoFitZoomIndex) {
+                        this.log(`⚠️ 已达到最小缩放级别 (${this.autoFitZoomIndex + 1}/${this.zoomSteps.length})`);
+                        return;
+                    }
+                    
                     this.currentZoomIndex--;
                     this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                     this.updatePixelsPerSecond();
                     this.log(`🔍 缩放级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length} (${this.zoomLevel}x)`);
                     document.getElementById('zoomSlider').value = this.zoomLevel;
                     this.updateZoom();
                 }
             }
@@ -750,6 +855,70 @@
                 });
             }
             
+            // 设置高度调整器
+            setupHeightAdjuster() {
+                // 在waveform区域底部添加高度调整条
+                const mainArea = document.querySelector('.main-area');
+                const heightAdjuster = document.createElement('div');
+                heightAdjuster.className = 'height-adjuster';
+                heightAdjuster.id = 'heightAdjuster';
+                
+                // 插入到main-area的最后
+                mainArea.appendChild(heightAdjuster);
+                
+                let isResizing = false;
+                let startY = 0;
+                let startHeight = 0;
+                
+                heightAdjuster.addEventListener('mousedown', (e) => {
+                    isResizing = true;
+                    this.isResizingHeight = true;
+                    startY = e.clientY;
+                    startHeight = mainArea.offsetHeight;
+                    document.body.style.cursor = 'ns-resize';
+                    e.preventDefault();
+                    
+                    this.log('📏 开始调整波形区域高度');
+                });
+                
+                document.addEventListener('mousemove', (e) => {
+                    if (!isResizing) return;
+                    
+                    const deltaY = e.clientY - startY;
+                    const containerHeight = document.querySelector('.test-container').offsetHeight;
+                    const newHeight = startHeight + deltaY;
+                    const minHeight = 200; // 最小高度
+                    const maxHeight = containerHeight * 0.7; // 最大70%
+                    
+                    const clampedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
+                    const percentage = (clampedHeight / containerHeight) * 100;
+                    
+                    mainArea.style.flex = `0 0 ${clampedHeight}px`;
+                    mainArea.style.maxHeight = `${clampedHeight}px`;
+                });
+                
+                document.addEventListener('mouseup', () => {
+                    if (isResizing) {
+                        isResizing = false;
+                        this.isResizingHeight = false;
+                        document.body.style.cursor = '';
+                        this.updateLayout(); // 重新调整整体布局
+                        this.log('📏 波形区域高度调整完成');
+                    }
+                });
+                
+                // 双击重置高度
+                heightAdjuster.addEventListener('dblclick', () => {
+                    const containerHeight = document.querySelector('.test-container').offsetHeight;
+                    const defaultHeight = containerHeight * 0.45; // 45%
+                    mainArea.style.flex = `0 0 ${defaultHeight}px`;
+                    mainArea.style.maxHeight = `${defaultHeight}px`;
+                    this.updateLayout();
+                    this.log('📏 重置波形区域高度为默认值');
+                });
+            }
+            
             // 设置纵轴交互
             setupAmplitudeAxisInteraction() {
                 const amplitudeAxis = document.getElementById('amplitudeAxis');
@@ -987,6 +1156,58 @@
                }
                
                const amplitudeScale = (zoomFactor / 10) * 0.9;
+               
+               // 清除现有内容
+               amplitudeAxis.innerHTML = '';
+               
+               // 添加网格线到波形容器
+               const waveformContainer = document.querySelector('.waveform-container');
+               if (waveformContainer) {
+                   // 清除旧的网格线
+                   waveformContainer.querySelectorAll('.amplitude-grid-line').forEach(line => line.remove());
+               }
+               
+               // 计算刻度值
+               this.calculateAmplitudeTicks(values, height, amplitudeScale, amplitudeAxis, waveformContainer);
+           }
+           
+           calculateAmplitudeTicks(values, height, amplitudeScale, amplitudeAxis, waveformContainer) {
+               // 为每个主要刻度值计算位置和显示
+               values.forEach((value, index) => {
+                   let normalizedValue = value;
+                   if (this.amplitudeUnit === 'db') normalizedValue = value / 60;
+                   if (this.amplitudeUnit === 'percent') normalizedValue = value / 100;
+                   if (this.amplitudeUnit === 'linear') normalizedValue = value / 32768;
+                   
+                   // 应用纵轴偏移
+                   const y = height / 2 - ((normalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);
+                   
+                   if (y >= 0 && y <= height) {
+                       // 主要刻度标签
+                       const label = document.createElement('div');
+                       label.className = 'amplitude-label';
+                       label.style.top = y + 'px';
+                       
+                       // 主要刻度线
+                       const majorTick = document.createElement('div');
+                       majorTick.className = 'amplitude-tick major';
+                       majorTick.style.top = (y - 0.5) + 'px';
+                       majorTick.style.height = '1px';
+                       
+                       // 网格线（添加到波形容器）
+                       if (waveformContainer && Math.abs(normalizedValue) > 0.01) { // 不为零线添加网格
+                           const gridLine = document.createElement('div');
+                           gridLine.className = 'amplitude-grid-line';
+                           gridLine.style.top = y + 'px';
+                           waveformContainer.appendChild(gridLine);
+                       }
+                       
+                       // 添加次要刻度（在主要刻度之间）
+                       if (index < values.length - 1) {
+                           this.addMinorTicks(value, values[index + 1], y, values.length > 10 ? 2 : 5, height, amplitudeScale, amplitudeAxis);
+                       }
+                       
+                       let labelText;
+                       switch (this.amplitudeUnit) {
+                           case 'db':
+                               labelText = value.toFixed(precision);
+                               break;
+                           case 'percent':
+                               labelText = value.toFixed(precision) + '%';
+                               break;
+                           case 'linear':
+                           default:
+                                labelText = value.toString();
+                               break;
+                       }
+                       
+                       label.textContent = labelText;
+                       amplitudeAxis.appendChild(majorTick);
+                       amplitudeAxis.appendChild(label);
+                   }
+               });
+           }
+           
+           addMinorTicks(startValue, endValue, startY, numMinorTicks, height, amplitudeScale, amplitudeAxis) {
+               const valueStep = (endValue - startValue) / (numMinorTicks + 1);
+               const yStep = -((endValue - startValue) / 32768 * height / 2 * amplitudeScale) / (numMinorTicks + 1);
+               
+               for (let i = 1; i <= numMinorTicks; i++) {
+                   const minorY = startY + (yStep * i);
+                   
+                   if (minorY >= 0 && minorY <= height) {
+                       const minorTick = document.createElement('div');
+                       minorTick.className = 'amplitude-tick minor';
+                       minorTick.style.top = (minorY - 0.5) + 'px';
+                       minorTick.style.height = '1px';
+                       amplitudeAxis.appendChild(minorTick);
+                   }
+               }
+           }
                
-               values.forEach(value => {
-                   let normalizedValue = value;
-                   if (this.amplitudeUnit === 'db') normalizedValue = value / 60;
-                   if (this.amplitudeUnit === 'percent') normalizedValue = value / 100;
-                   if (this.amplitudeUnit === 'linear') normalizedValue = value / 32768; // 16位音頻標準化
-                   
-                   // 应用纵轴偏移
-                   const y = height / 2 - ((normalizedValue + this.amplitudeOffset) * height / 2 * amplitudeScale);
-                   
-                   if (y >= 0 && y <= height) {
-                       const label = document.createElement('div');
-                       label.className = 'amplitude-label';
-                       label.style.top = y + 'px';
-                       
-                       let labelText;
-                       switch (this.amplitudeUnit) {
-                           case 'db':
-                               labelText = value.toFixed(precision);
-                               break;
-                           case 'percent':
-                               labelText = value.toFixed(precision) + '%';
-                               break;
-                           case 'linear':
-                           default:
-                                labelText = value.toString();
-                               break;
-                       }
-                       
-                       label.textContent = labelText;
-                       amplitudeAxis.appendChild(label);
-                   }
-               });
-           }
            
            updateLayout() {
                // 同步更新时间轴和縱軸
@@ -1030,6 +1251,12 @@
                     
                     const autoFitZoom = Math.max(0.1, requiredPixelsPerSecond / 10);
                     
+                    // 记录自动适配的缩放级别
+                    this.autoFitZoomIndex = Math.max(0, this.zoomSteps.findIndex(zoom => zoom >= autoFitZoom));
+                    if (this.autoFitZoomIndex === -1) {
+                        this.autoFitZoomIndex = 0;
+                    }
+                    this.hasAutoFitted = true;
+                    
                     // 使用固定阶数缩放
                     this.setZoomLevel(autoFitZoom);

                     // 更新滑块范围
                     const slider = document.getElementById('zoomSlider');
                     if (slider) {
                         slider.min = this.zoomSteps[0];
                         slider.max = this.zoomSteps[this.zoomSteps.length - 1];
                         slider.value = this.zoomLevel;
                     }

                     // 重置视角偏移
                     this.viewOffset = 0;
                     this.amplitudeOffset = 0; // 重置纵轴偏移
                     this.updateZoom();
                     this.updateAmplitudeAxis();
-                    this.log(`🔍 自动缩放: ${this.zoomLevel.toFixed(2)}倍，级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length}`);
+                    this.log(`🔍 自动缩放: ${this.zoomLevel.toFixed(2)}倍，级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length}，最小级别: ${this.autoFitZoomIndex + 1}`);
                 }
             }