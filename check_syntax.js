const fs = require('fs');

// 读取HTML文件
const content = fs.readFileSync('audio_visual.html', 'utf8');

// 提取JavaScript代码
const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/);
if (!scriptMatch) {
    console.log('No script tag found');
    process.exit(1);
}

const jsCode = scriptMatch[1];

// 简单的括号匹配检查
let openBraces = 0;
let openParens = 0;
let openBrackets = 0;
let inString = false;
let stringChar = '';
let inComment = false;
let inMultiLineComment = false;

for (let i = 0; i < jsCode.length; i++) {
    const char = jsCode[i];
    const nextChar = jsCode[i + 1];
    
    // 处理注释
    if (!inString && !inComment && !inMultiLineComment) {
        if (char === '/' && nextChar === '/') {
            inComment = true;
            continue;
        }
        if (char === '/' && nextChar === '*') {
            inMultiLineComment = true;
            continue;
        }
    }
    
    if (inComment && char === '\n') {
        inComment = false;
        continue;
    }
    
    if (inMultiLineComment && char === '*' && nextChar === '/') {
        inMultiLineComment = false;
        i++; // 跳过下一个字符
        continue;
    }
    
    if (inComment || inMultiLineComment) continue;
    
    // 处理字符串
    if (!inString && (char === '"' || char === "'" || char === '`')) {
        inString = true;
        stringChar = char;
        continue;
    }
    
    if (inString && char === stringChar && jsCode[i-1] !== '\\') {
        inString = false;
        stringChar = '';
        continue;
    }
    
    if (inString) continue;
    
    // 计算括号
    switch (char) {
        case '{':
            openBraces++;
            break;
        case '}':
            openBraces--;
            break;
        case '(':
            openParens++;
            break;
        case ')':
            openParens--;
            break;
        case '[':
            openBrackets++;
            break;
        case ']':
            openBrackets--;
            break;
    }
}

console.log(`Open braces: ${openBraces}`);
console.log(`Open parentheses: ${openParens}`);
console.log(`Open brackets: ${openBrackets}`);

if (openBraces > 0) {
    console.log(`Missing ${openBraces} closing brace(s)`);
} else if (openBraces < 0) {
    console.log(`Extra ${-openBraces} closing brace(s)`);
}

if (openParens > 0) {
    console.log(`Missing ${openParens} closing parenthesis(es)`);
} else if (openParens < 0) {
    console.log(`Extra ${-openParens} closing parenthesis(es)`);
}

if (openBrackets > 0) {
    console.log(`Missing ${openBrackets} closing bracket(s)`);
} else if (openBrackets < 0) {
    console.log(`Extra ${-openBrackets} closing bracket(s)`);
}
