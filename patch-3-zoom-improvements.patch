
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sat, 14 Jun 2025 11:00:00 +0800
Subject: [PATCH 3/3] 完善时间轴显示逻辑和缩放修复

---
 audio_visual.html | 87 +++++++++++++++++++++++++++++++++++++++++++++----------
 1 <USER> <GROUP>, 72 insertions(+), 15 deletions(-)

diff --git a/audio_visual.html b/audio_visual.html
index fedcba9..9876543 100644
--- a/audio_visual.html
+++ b/audio_visual.html
@@ -1845,6 +1845,25 @@
             // 固定阶数缩放方法
             // 縮放控制
             zoomIn() {
+                const wasAtAutoFitLevel = this.hasAutoFitted && this.currentZoomIndex === this.autoFitZoomIndex;
                 
                 if (this.currentZoomIndex < this.zoomSteps.length - 1) {
                     this.currentZoomIndex++;
                     this.zoomLevel = this.zoomSteps[this.currentZoomIndex];
                     this.updatePixelsPerSecond();
+                    
+                    // 如果从自动适配级别放大，保持视角在音频起始位置
+                    if (wasAtAutoFitLevel) {
+                        this.viewOffset = 0;
+                    }
+                    
                     this.log(`🔍 缩放级别: ${this.currentZoomIndex + 1}/${this.zoomSteps.length} (${this.zoomLevel}x)`);
                     document.getElementById('zoomSlider').value = this.zoomLevel;
                     this.updateZoom();
                 }
-                
-          
-                // 如果zoom out到最小級別，重置視角
-                if (this.zoomLevel <= this.minZoomLevel) {
-                    this.viewOffset = 0;
-                }
-
             }
             
             zoomOut() {
                 if (this.currentZoomIndex > 0) {
                     // 检查是否会超出自动适配的最小级别
                     if (this.hasAutoFitted && this.currentZoomIndex - 1 < this.autoFitZoomIndex) {
                         this.log(`⚠️ 已达到最小缩放级别 (${this.autoFitZoomIndex + 1}/${this.zoomSteps.length})`);
                         return;
                     }
+